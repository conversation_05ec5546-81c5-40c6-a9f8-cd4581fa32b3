# Get GID and UID for Docker build
GID := $(shell id -g)
UID := $(shell id -u)

# Define directories
BASE_DIR := $(shell pwd)
DOCKER_DIR := $(BASE_DIR)/infra/docker
COMPOSE_DIR := $(BASE_DIR)/infra/compose

# Docker Compose files
COMPOSE_DEV := $(COMPOSE_DIR)/compose.dev.yml
COMPOSE_PROD := $(COMPOSE_DIR)/compose.prod.yml
COMPOSE_LOCAL := $(COMPOSE_DIR)/compose.local.yml

# Environment files
ENV_FILE := $(BASE_DIR)/.env
ENV_EXAMPLE_FILE ?= $(BASE_DIR)/.env.example

# Project name
PROJECT_NAME := devxhub_django_admin_boilerplate

# Common Docker Compose options
DC_REMOVE_ORPHANS := --remove-orphans
DC_BUILD_ARGS := --pull --no-cache

# Docker Compose command shortcuts with env file
DOCKER_COMPOSE_BASE := docker compose -p $(PROJECT_NAME) --env-file $(ENV_FILE)
DOCKER_COMPOSE_DEV := $(DOCKER_COMPOSE_BASE) -f $(COMPOSE_DEV)
DOCKER_COMPOSE_PROD := $(DOCKER_COMPOSE_BASE) -f $(COMPOSE_PROD)
DOCKER_COMPOSE_LOCAL := $(DOCKER_COMPOSE_BASE) -f $(COMPOSE_LOCAL)

# Docker image details
DOCKER_IMAGE := dxhltd/django-admin-boilerplate
DOCKERFILE_PROD := $(DOCKER_DIR)/Dockerfile.prod
DOCKERFILE_LOCAL := $(DOCKER_DIR)/Dockerfile.local
DOCKER_IMAGE_TAG ?= local

# Colors for pretty output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
CYAN := \033[36m
RESET := \033[0m

# Targets
.PHONY: all dev prod local stop clean logs rebuild push push-local build build-local \
        restart-dev restart-prod restart-local pull pull-local test shell help check-env ps \
        validate-compose health-check-dev health-check-prod health-check-local \
        deps-check deps-update backup restore

# Default target
all: help

# Validation
validate-compose:
	@for file in $(COMPOSE_DEV) $(COMPOSE_PROD) $(COMPOSE_LOCAL); do \
		if [ ! -f $$file ]; then \
			echo "$(RED)Missing compose file: $$file$(RESET)"; \
			exit 1; \
		fi \
	done

# Ensure .env file exists
check-env: ## Check if .env file exists and create if missing
	@if [ ! -f $(ENV_FILE) ]; then \
		if [ -f $(ENV_EXAMPLE_FILE) ]; then \
			cp $(ENV_EXAMPLE_FILE) $(ENV_FILE); \
			echo "$(YELLOW)Created $(ENV_FILE) from $(ENV_EXAMPLE_FILE)$(RESET)"; \
			echo "$(YELLOW)Please review and update $(ENV_FILE) with your settings$(RESET)"; \
		else \
			echo "$(RED)Error: Neither $(ENV_FILE) nor $(ENV_EXAMPLE_FILE) found$(RESET)"; \
			echo "$(RED)Please create $(ENV_FILE) manually or provide $(ENV_EXAMPLE_FILE)$(RESET)"; \
			exit 1; \
		fi \
	else \
		if ! grep -q "." $(ENV_FILE); then \
			echo "$(RED)Warning: $(ENV_FILE) appears to be empty$(RESET)"; \
			exit 1; \
		fi \
	fi; \
	if grep -q "^UID=" $(ENV_FILE); then \
		sed -i.bak 's/^UID=.*/UID=$(UID)/' $(ENV_FILE); \
	else \
		echo "UID=$(UID)" >> $(ENV_FILE); \
	fi; \
	if grep -q "^GID=" $(ENV_FILE); then \
		sed -i.bak 's/^GID=.*/GID=$(GID)/' $(ENV_FILE); \
	else \
		echo "GID=$(GID)" >> $(ENV_FILE); \
	fi; \
	rm -f $(ENV_FILE).bak; \
	echo "$(GREEN)Updated $(ENV_FILE) with UID=$(UID) and GID=$(GID)$(RESET)"

# Development environment
dev: check-env validate-compose ## Start the development environment
	@echo "$(CYAN)Starting development environment...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) build --build-arg GID=$(GID) --build-arg UID=$(UID)
	@$(DOCKER_COMPOSE_DEV) up

# Production environment
prod: check-env validate-compose ## Start the production environment
	@echo "$(CYAN)Starting production environment...$(RESET)"
	@$(DOCKER_COMPOSE_PROD) pull
	@$(DOCKER_COMPOSE_PROD) down
	@$(DOCKER_COMPOSE_PROD) up -d

# Local production-like environment
local: check-env validate-compose ## Start the production-like environment locally
	@echo "$(CYAN)Starting local production-like environment...$(RESET)"
	@$(DOCKER_COMPOSE_LOCAL) pull
	@$(DOCKER_COMPOSE_LOCAL) down
	@$(DOCKER_COMPOSE_LOCAL) up -d

# Stop services
stop-dev: check-env ## Stop development services
	@echo "$(CYAN)Stopping development services...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) down

stop-prod: check-env ## Stop production services
	@echo "$(CYAN)Stopping production services...$(RESET)"
	@$(DOCKER_COMPOSE_PROD) down

stop-local: check-env ## Stop local production-like services
	@echo "$(CYAN)Stopping local services...$(RESET)"
	@$(DOCKER_COMPOSE_LOCAL) down

stop: stop-dev stop-prod stop-local ## Stop all running services

# Clean up Docker volumes
clean-dev: check-env ## Clean development environment
	@echo "$(YELLOW)Cleaning development environment...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) down -v

clean-prod: check-env ## Clean production environment
	@echo "$(YELLOW)Cleaning production environment...$(RESET)"
	@$(DOCKER_COMPOSE_PROD) down -v

clean-local: check-env ## Clean local production-like environment
	@echo "$(YELLOW)Cleaning local environment...$(RESET)"
	@$(DOCKER_COMPOSE_LOCAL) down -v

clean: clean-dev clean-prod clean-local ## Clean all environments

# Show running containers
ps: ## Show running containers
	@echo "$(CYAN)Development containers:$(RESET)"
	@$(DOCKER_COMPOSE_DEV) ps
	@echo "$(CYAN)Production containers:$(RESET)"
	@$(DOCKER_COMPOSE_PROD) ps
	@echo "$(CYAN)Local containers:$(RESET)"
	@$(DOCKER_COMPOSE_LOCAL) ps

# Health checks
health-check-dev: ## Check health of development services
	@echo "$(CYAN)Checking development services health...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) ps --format json | jq -r '.[] | select(.State != "running") | .Name' | \
		if [ -n "$$(cat)" ]; then \
			echo "$(RED)Some services are not running:$(RESET)"; cat; exit 1; \
		else \
			echo "$(GREEN)All services are running$(RESET)"; \
		fi

health-check-prod: ## Check health of production services
	@echo "$(CYAN)Checking production services health...$(RESET)"
	@$(DOCKER_COMPOSE_PROD) ps --format json | jq -r '.[] | select(.State != "running") | .Name' | \
		if [ -n "$$(cat)" ]; then \
			echo "$(RED)Some services are not running:$(RESET)"; cat; exit 1; \
		else \
			echo "$(GREEN)All services are running$(RESET)"; \
		fi

health-check-local: ## Check health of local services
	@echo "$(CYAN)Checking local services health...$(RESET)"
	@$(DOCKER_COMPOSE_LOCAL) ps --format json | jq -r '.[] | select(.State != "running") | .Name' | \
		if [ -n "$$(cat)" ]; then \
			echo "$(RED)Some services are not running:$(RESET)"; cat; exit 1; \
		else \
			echo "$(GREEN)All services are running$(RESET)"; \
		fi

# Restart services
restart-dev: stop-dev dev ## Restart development services
restart-prod: stop-prod prod ## Restart production services
restart-local: stop-local local ## Restart local production-like services

# Rebuild images
rebuild: check-env ## Rebuild development images
	@echo "$(CYAN)Rebuilding development images...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) build $(DC_BUILD_ARGS)

# Build Docker images
build: ## Build production image
	@if [ -z "$$tag" ]; then \
		read -p "Enter image tag (default: latest): " tag; \
		tag=$${tag:-latest}; \
	fi; \
	echo "$(CYAN)Building image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--tag $(DOCKER_IMAGE):$$tag \
		--cache-to mode=max,type=registry,ref=$(DOCKER_IMAGE):$$tag-cache \
		--cache-from type=registry,ref=$(DOCKER_IMAGE):$$tag-cache \
		-f $(DOCKERFILE_PROD) $(BASE_DIR) || \
		{ echo "$(RED)Build failed$(RESET)"; exit 1; }

build-local: check-env ## Build production-like image for local use
	@if [ -z "$$tag" ]; then \
		read -p "Enter image tag (default: local): " tag; \
		tag=$${tag:-local}; \
	fi; \
	echo "$(CYAN)Building local image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker buildx build \
		--platform linux/amd64 \
		--tag $(DOCKER_IMAGE):$$tag \
		-f $(DOCKERFILE_LOCAL) $(BASE_DIR) || \
		{ echo "$(RED)Build failed$(RESET)"; exit 1; }

build-dev: check-env ## Build production-like image for local use
	@if [ -z "$$tag" ]; then \
		read -p "Enter image tag (default: local): " tag; \
		tag=$${tag:-local}; \
	fi; \
	echo "$(CYAN)Building local image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--tag $(DOCKER_IMAGE):$$tag \
		-f $(DOCKERFILE_LOCAL) $(BASE_DIR) || \
		{ echo "$(RED)Build failed$(RESET)"; exit 1; }

# Push Docker images
push: build ## Build and push production image
	@echo "$(CYAN)Pushing image $(DOCKER_IMAGE):$$tag$(RESET)"
	@docker push $(DOCKER_IMAGE):$$tag

push-local: check-env ## Build production-like image for local use
	@if [ -z "$$tag" ]; then \
		read -p "Enter image tag (default: local): " tag; \
		tag=$${tag:-local}; \
	fi; \
	echo "$(CYAN)Building local image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker buildx build \
		--push \
		--platform linux/amd64 \
		--tag $(DOCKER_IMAGE):$$tag \
		-f $(DOCKERFILE_LOCAL) $(BASE_DIR) || \
		{ echo "$(RED)Build failed$(RESET)"; exit 1; }

# Pull Docker images
pull: ## Pull production image
	@read -p "Enter image tag (default: latest): " tag; \
	tag=$${tag:-latest}; \
	echo "$(CYAN)Pulling image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker pull $(DOCKER_IMAGE):$$tag

pull-local: ## Pull local image
	@read -p "Enter image tag (default: local): " tag; \
	tag=$${tag:-local}; \
	echo "$(CYAN)Pulling local image $(DOCKER_IMAGE):$$tag$(RESET)"; \
	docker pull $(DOCKER_IMAGE):$$tag

# Dependency management
deps-check: ## Check for outdated dependencies
	@echo "$(CYAN)Checking for outdated dependencies...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) exec django pip list --outdated

deps-update: ## Update project dependencies
	@echo "$(CYAN)Updating dependencies...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) exec django pip install --upgrade -r requirements/production.txt

# Database management
backup: ## Backup database
	@echo "$(CYAN)Creating database backup...$(RESET)"
	@mkdir -p backups && \
		$(DOCKER_COMPOSE_PROD) exec postgres pg_dump -U devxhub > backups/backup_$$(date +%Y%m%d_%H%M%S).sql && \
		echo "$(GREEN)Backup created successfully$(RESET)" || \
		{ echo "$(RED)Backup failed$(RESET)"; exit 1; }

restore: ## Restore database from backup
	@read -p "Enter backup file: " file; \
	if [ -f "$$file" ]; then \
		echo "$(CYAN)Restoring from $$file...$(RESET)"; \
		$(DOCKER_COMPOSE_PROD) exec -T postgres psql -U devxhub < $$file && \
		echo "$(GREEN)Database restored successfully$(RESET)" || \
		{ echo "$(RED)Restore failed$(RESET)"; exit 1; }; \
	else \
		echo "$(RED)Backup file not found: $$file$(RESET)"; \
		exit 1; \
	fi

# View logs for any environment
logs: ## View logs (dev/prod/local)
	@read -p "Enter environment (dev/prod/local, default: prod): " env; \
	env=$${env:-prod}; \
	case $$env in \
		dev) cmd="$(DOCKER_COMPOSE_DEV) logs -f" ;; \
		prod) cmd="$(DOCKER_COMPOSE_PROD) logs -f" ;; \
		local) cmd="$(DOCKER_COMPOSE_LOCAL) logs -f" ;; \
		*) echo "$(YELLOW)Invalid environment: $$env. Use dev, prod, or local.$(RESET)"; exit 1 ;; \
	esac; \
	echo "$(CYAN)Showing logs for $$env environment...$(RESET)"; \
	eval $$cmd

# Open shell in Django container
shell: check-env ## Open shell in django container
	@echo "$(CYAN)Opening shell in django container...$(RESET)"
	@$(DOCKER_COMPOSE_DEV) exec django /bin/bash

# Help menu (auto-generated)
help: ## Show this help message
	@echo "$(CYAN)Available commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## ' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = "(:|##)"}; {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$3}'
