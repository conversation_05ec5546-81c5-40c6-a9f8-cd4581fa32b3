#!/bin/bash

# Strict mode settings
set -euo pipefail
IFS=$'\n\t'

# Default environment variables
export WSGI_PORT="${WSGI_PORT:-8000}"
export ASGI_PORT="${ASGI_PORT:-8001}"
export MAX_STARTUP_RETRY="${MAX_STARTUP_RETRY:-50}"
export STARTUP_RETRY_INTERVAL="${STARTUP_RETRY_INTERVAL:-5}"
export GRACEFUL_TIMEOUT="${GRACEFUL_TIMEOUT:-30}"

# Process ID tracking
declare -a PIDS=()

log() { echo "$(date +'%Y-%m-%d %H:%M:%S') [INFO] $*" >&2; }
log_error() { echo "$(date +'%Y-%m-%d %H:%M:%S') [ERROR] $*" >&2; }
log_warning() { echo "$(date +'%Y-%m-%d %H:%M:%S') [WARNING] $*" >&2; }

# Check if required services are available
check_service() {
    local host=$1 port=$2 service=$3
    for i in $(seq 1 "$MAX_STARTUP_RETRY"); do
        if nc -z "$host" "$port"; then
            log "$service is up on $host:$port"
            return 0
        fi
        log_warning "Waiting for $service ($host:$port)... attempt $i/$MAX_STARTUP_RETRY"
        sleep "$STARTUP_RETRY_INTERVAL"
    done
    log_error "$service failed to start!"
    return 1
}

# Graceful shutdown handler
cleanup() {
    log "Initiating graceful shutdown..."
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid" 2>/dev/null || true
        fi
    done

    timeout "$GRACEFUL_TIMEOUT" wait "${PIDS[@]}" || log_warning "Some processes did not exit cleanly"

    # Force kill any remaining processes
    for pid in "${PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "Force killing process $pid"
            kill -9 "$pid" 2>/dev/null || true
        fi
    done

    log "Shutdown complete"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT SIGQUIT

# Apply database migrations
log "Applying database migrations..."
python manage.py migrate --noinput

# Ensure Django superuser exists
if [[ -n "${DJANGO_SUPERUSER_USERNAME:-}" && -n "${DJANGO_SUPERUSER_EMAIL:-}" && -n "${DJANGO_SUPERUSER_PASSWORD:-}" ]]; then
    log "Ensuring Django superuser exists..."
    python manage.py shell <<EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username="${DJANGO_SUPERUSER_USERNAME}").exists():
    User.objects.create_superuser("${DJANGO_SUPERUSER_USERNAME}", "${DJANGO_SUPERUSER_EMAIL}", "${DJANGO_SUPERUSER_PASSWORD}")
EOF
fi

# Start Django Development Server (WSGI)
log "Starting Django development server..."
python manage.py runserver 0.0.0.0:$WSGI_PORT & 
PIDS+=($!)

# Start Uvicorn (ASGI for Django Channels)
log "Starting Uvicorn (ASGI)..."
uvicorn config.asgi:application --host 0.0.0.0 --port "$ASGI_PORT" --reload --log-level info & 
PIDS+=($!)

# Start Celery worker (if enabled)

log "Starting Celery worker..."
celery -A config worker --loglevel=info & 
PIDS+=($!)

log "Starting Celery beat..."
celery -A config beat --loglevel=info & 
PIDS+=($!)


# Health checks for Django and Uvicorn
check_service "localhost" "$WSGI_PORT" "Django runserver"
check_service "localhost" "$ASGI_PORT" "Uvicorn"

# Wait for all processes and handle failures
while true; do
    for pid in "${PIDS[@]}"; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_error "Process $pid has died unexpectedly"
            cleanup
            exit 1
        fi
    done
    sleep 1
done
