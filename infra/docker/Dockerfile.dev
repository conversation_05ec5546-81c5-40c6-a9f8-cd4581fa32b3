# syntax=docker.io/docker/dockerfile:1

# Stage 1: Build dependencies using Python
FROM python:3.13 AS builder

ENV PYTHONDONTWRITEBYTECODE=1 \
  PYTHONUNBUFFERED=1 \
  PIP_NO_CACHE_DIR=1 \
  PIP_DISABLE_PIP_VERSION_CHECK=1 \
  PIP_DEFAULT_TIMEOUT=100

# Install build dependencies and remove them after use
RUN apt-get update && apt-get install --no-install-recommends -y \
  build-essential \
  curl \
  libpq-dev \
  libffi-dev \
  libssl-dev \
  gdal-bin \
  libgdal-dev \
  && rm -rf /var/lib/apt/lists/*

WORKDIR /build
COPY requirements /build/requirements
COPY packages /build/packages 

# Build wheels for dependencies
RUN pip wheel --no-cache-dir --wheel-dir /wheels -r requirements/local.txt \
  && apt-get purge -y --auto-remove build-essential libpq-dev libffi-dev libssl-dev \
  && rm -rf /var/lib/apt/lists/*

# Stage 2: Final stage with Python slim
FROM python:3.13-slim-bookworm

ENV PYTHONDONTWRITEBYTECODE=1 \
  PYTHONUNBUFFERED=1 \
  DJANGO_SETTINGS_MODULE=config.settings.development \
  GUNICORN_WORKERS=3 \
  GUNICORN_TIMEOUT=120 \
  GUNICORN_WORKER_CONNECTIONS=1000 \
  APP_HOME=/app

# Install only required runtime dependencies and clean up
RUN apt-get update && apt-get install --no-install-recommends -y \
  libpq5 \
  libjpeg62-turbo \
  libfreetype6 \
  netcat-traditional \
  gettext \
  && rm -rf /var/lib/apt/lists/*

# Create a non-root user
ARG UID=1001
ARG GID=1001

RUN getent group $GID || addgroup --gid $GID django \
  && adduser --disabled-password --gecos "" --uid $UID --gid $GID --home /home/<USER>/bin/bash django \
  && mkdir -p /app/staticfiles /app/media \
  && chown -R $UID:$GID /app

# Copy pre-built wheels and install dependencies.
COPY --from=builder /wheels /wheels
COPY --from=builder /build/requirements/local.txt .
COPY --from=builder /build/requirements/common.txt .
COPY --from=builder /build/packages /packages 

RUN pip install --no-cache-dir --no-index --find-links=/wheels -r local.txt \
  && rm -rf /wheels local.txt common.txt

# Switch to the non-root user
USER django
WORKDIR $APP_HOME

# Copy the application source code
COPY --chown=django:django . .

# Setup entrypoint
COPY --chown=django:django infra/docker/entrypoint.dev.sh /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint

EXPOSE 8000 8001

CMD ["docker-entrypoint"]
