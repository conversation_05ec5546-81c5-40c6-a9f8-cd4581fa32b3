#!/usr/bin/env python
"""
Test script to verify LinkedIn OAuth2 fix
"""
import os
import sys
import django

# Add project paths
sys.path.append('.')
sys.path.append('packages/dxh-library')
sys.path.append('packages/dxh-common')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_linkedin_provider():
    """Test the custom LinkedIn provider"""
    try:
        from dxh_libraries.allauth import CustomLinkedInOAuth2Provider
        
        # Create provider instance
        provider = CustomLinkedInOAuth2Provider(None)
        
        # Test with new format (sub field from /v2/userinfo)
        new_format_data = {
            'sub': '12345',
            'email': '<EMAIL>',
            'given_name': '<PERSON>',
            'family_name': '<PERSON><PERSON>',
            'name': '<PERSON>'
        }
        
        uid = provider.extract_uid(new_format_data)
        print(f"✅ New format UID extraction: {uid}")
        
        fields = provider.extract_common_fields(new_format_data)
        print(f"✅ Common fields extraction: {fields}")
        
        # Test with old format (id field from /v2/me)
        old_format_data = {
            'id': '67890',
            'email': '<EMAIL>',
            'firstName': 'Jane',
            'lastName': 'Smith'
        }
        
        uid = provider.extract_uid(old_format_data)
        print(f"✅ Old format UID extraction: {uid}")
        
        print("✅ All LinkedIn provider tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ LinkedIn provider test failed: {e}")
        return False

def test_linkedin_adapter():
    """Test the custom LinkedIn adapter"""
    try:
        from dxh_libraries.allauth import LinkedInOAuth2Adapter
        
        # Create adapter instance
        adapter = LinkedInOAuth2Adapter()
        
        # Check profile URL
        expected_url = 'https://api.linkedin.com/v2/userinfo'
        if hasattr(adapter, 'profile_url') and adapter.profile_url == expected_url:
            print(f"✅ Profile URL correctly set: {adapter.profile_url}")
        else:
            print(f"❌ Profile URL incorrect: {getattr(adapter, 'profile_url', 'Not found')}")
            return False
        
        print("✅ LinkedIn adapter test passed!")
        return True
        
    except Exception as e:
        print(f"❌ LinkedIn adapter test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing LinkedIn OAuth2 fix...")
    
    provider_test = test_linkedin_provider()
    adapter_test = test_linkedin_adapter()
    
    if provider_test and adapter_test:
        print("\n🎉 All tests passed! LinkedIn OAuth2 fix is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)
