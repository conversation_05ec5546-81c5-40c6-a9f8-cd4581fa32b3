from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import (
    FacebookOAuth2Adapter,
)
from allauth.socialaccount.providers.linkedin_oauth2.views import (
    LinkedInOAuth2Adapter,
)
import logging
import requests

logger = logging.getLogger(__name__)


class GoogleOAuth2Adapter(GoogleOAuth2Adapter):
    pass


class Facebook<PERSON>uth2Adapter(FacebookOAuth2Adapter):
    pass


class LinkedInOAuth2Adapter(LinkedInOAuth2Adapter):
    """
    Custom LinkedIn OAuth2 Adapter to handle LinkedIn's specific token response format.
    LinkedIn returns both access_token and id_token, similar to Google's OpenID Connect flow.
    This completely overrides the default LinkedIn adapter to use the new /v2/userinfo endpoint.
    """

    def complete_login(self, request, app, token, **kwargs):
        """
        Override complete_login to properly handle LinkedIn's token response.
        LinkedIn provides user info through the new /v2/userinfo endpoint.
        """
        from allauth.socialaccount.models import <PERSON>Log<PERSON>, SocialAccount
        from allauth.socialaccount.providers.linkedin_oauth2.provider import LinkedInOAuth2Provider
        from django.contrib.auth import get_user_model

        try:
            # Get user info from LinkedIn API using the access token
            headers = {
                'Authorization': f'Bearer {token.token}',
                'Content-Type': 'application/json',
            }

            # LinkedIn v2 API endpoint for user profile (new endpoint)
            profile_url = 'https://api.linkedin.com/v2/userinfo'

            response = requests.get(profile_url, headers=headers)
            response.raise_for_status()

            extra_data = response.json()
            logger.info(f"LinkedIn userinfo response: {extra_data}")

            # Create social login manually since we're not calling parent
            provider = LinkedInOAuth2Provider(request)

            # Extract user data from the response
            uid = extra_data.get('sub')  # LinkedIn uses 'sub' as the unique identifier
            if not uid:
                raise ValueError("No 'sub' field found in LinkedIn userinfo response")

            # Create social account
            social_account = SocialAccount(
                provider=provider.id,
                uid=uid,
                extra_data=extra_data
            )

            # Create social login
            social_login = SocialLogin(
                account=social_account,
                token=token
            )

            # Create a temporary user object with the data from LinkedIn
            User = get_user_model()
            user = User()

            # Populate user data from LinkedIn response
            user.email = extra_data.get('email', '')
            user.first_name = extra_data.get('given_name', '')
            user.last_name = extra_data.get('family_name', '')
            user.username = extra_data.get('email', '')  # Use email as username

            # Set the user on the social login
            social_login.user = user
            social_login.account.user = user

            return social_login

        except requests.RequestException as e:
            logger.error(f"LinkedIn API request failed: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"LinkedIn complete_login failed: {str(e)}")
            raise