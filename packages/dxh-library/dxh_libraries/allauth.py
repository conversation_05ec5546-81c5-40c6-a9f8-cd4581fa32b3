from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount import providers
from allauth.socialaccount.admin import SocialAppForm
from allauth.socialaccount.providers.facebook.views import (
    FacebookOAuth2Adapter,
)
from allauth.socialaccount.providers.linkedin_oauth2.views import (
    LinkedInOAuth2Adapter,
)
from allauth.socialaccount.models import SocialApp
import logging

logger = logging.getLogger(__name__)


class GoogleOAuth2Adapter(GoogleOAuth2Adapter):
    pass


class FacebookOAuth2Adapter(FacebookOAuth2Adapter):
    pass


class LinkedInOAuth2Adapter(LinkedInOAuth2Adapter):
    pass