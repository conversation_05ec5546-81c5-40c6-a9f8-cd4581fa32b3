from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount import providers
from allauth.socialaccount.admin import SocialA<PERSON>Form
from allauth.socialaccount.providers.facebook.views import (
    FacebookOAuth2Adapter,
)
from allauth.socialaccount.providers.linkedin_oauth2.views import (
    LinkedInOAuth2Adapter,
)
from allauth.socialaccount.providers.linkedin_oauth2.provider import LinkedInOAuth2Provider
from allauth.socialaccount.models import SocialApp
import logging

logger = logging.getLogger(__name__)


class GoogleOAuth2Adapter(GoogleOAuth2Adapter):
    pass


class FacebookOAuth2Adapter(FacebookOAuth2Adapter):
    pass


class CustomLinkedInOAuth2Provider(LinkedInOAuth2Provider):
    """
    Custom LinkedIn OAuth2 Provider to handle the new /v2/userinfo response format.
    LinkedIn now returns the user ID in the 'sub' field instead of 'id'.
    """

    def extract_uid(self, data):
        """
        Extract user ID from LinkedIn's /v2/userinfo response.
        LinkedIn uses 'sub' field for the unique identifier in the new endpoint.
        """
        # Try the new format first (sub field from /v2/userinfo)
        uid = data.get('sub')
        if uid:
            return uid

        # Fall back to the old format (id field from /v2/me) for backward compatibility
        uid = data.get('id')
        if uid:
            return uid

        # If neither field is found, raise an exception
        logger.error(f"LinkedIn response missing both 'sub' and 'id' fields: {data}")
        raise ValueError("LinkedIn response missing user identifier")

    def extract_common_fields(self, data):
        """
        Extract common user fields from LinkedIn's /v2/userinfo response.
        """
        return {
            'email': data.get('email'),
            'first_name': data.get('given_name', ''),
            'last_name': data.get('family_name', ''),
            'name': data.get('name', ''),
        }


class LinkedInOAuth2Adapter(LinkedInOAuth2Adapter):
    """
    Custom LinkedIn OAuth2 Adapter to use the new /v2/userinfo endpoint
    and handle the updated response format.
    """

    # Override the profile URL to use the new LinkedIn userinfo endpoint
    profile_url = 'https://api.linkedin.com/v2/userinfo'

    def get_provider(self):
        """
        Return our custom LinkedIn provider that handles the new response format.
        """
        return CustomLinkedInOAuth2Provider(self.request)