from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import (
    FacebookOAuth2Adapter,
)
from allauth.socialaccount.providers.linkedin_oauth2.views import (
    LinkedInOAuth2Adapter,
)
import logging
import requests

logger = logging.getLogger(__name__)


class GoogleOAuth2Adapter(GoogleOAuth2Adapter):
    pass


class Facebook<PERSON>uth2Adapter(FacebookOAuth2Adapter):
    pass


class LinkedInOAuth2Adapter(LinkedInOAuth2Adapter):
    """
    Custom LinkedIn OAuth2 Adapter to handle LinkedIn's specific token response format.
    LinkedIn returns both access_token and id_token, similar to Google's OpenID Connect flow.
    """

    def complete_login(self, request, app, token, **kwargs):
        """
        Override complete_login to properly handle LinkedIn's token response.
        LinkedIn provides user info through both the access token and id_token.
        """
        try:
            # Get user info from LinkedIn API using the access token
            headers = {
                'Authorization': f'Bearer {token.token}',
                'Content-Type': 'application/json',
            }

            # LinkedIn v2 API endpoint for user profile
            profile_url = 'https://api.linkedin.com/v2/userinfo'

            response = requests.get(profile_url, headers=headers)
            response.raise_for_status()

            extra_data = response.json()

            # Call the parent method with the extra data
            login = super().complete_login(request, app, token, **kwargs)

            # Update the social account with the extra data
            if login.account:
                login.account.extra_data = extra_data

            return login

        except requests.RequestException as e:
            logger.error(f"LinkedIn API request failed: {str(e)}")
            # Fall back to the default implementation
            return super().complete_login(request, app, token, **kwargs)