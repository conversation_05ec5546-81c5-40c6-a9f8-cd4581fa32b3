import json
import datetime
from celery.utils.log import get_task_logger
from django.utils import timezone
from django_celery_beat.models import PeriodicTask, CrontabSchedule
from dxh_libraries.celery import shared_task

logger = get_task_logger(__name__)


def logged_task(*task_args, **task_kwargs):
    def decorator(func):
        @shared_task(*task_args, **task_kwargs)
        def wrapper(*args, **kwargs):
            logger.info(
                f"Task {func.__name__} started with args: {args} and kwargs: {kwargs}"
            )
            result = func(*args, **kwargs)
            logger.info(
                f"Task {func.__name__} completed with result: {result}"
            )
            return result

        return wrapper

    return decorator

def schedul_task(task, task_name, scheduled_time):
    if not scheduled_time:
        return False

    schedule, _ = CrontabSchedule.objects.get_or_create(
        minute=scheduled_time.minute,
        hour=scheduled_time.hour,
        day_of_month=scheduled_time.day,
        month_of_year=scheduled_time.month,
        day_of_week='*',
    )

    task = PeriodicTask.objects.update_or_create(
        name=task_name,
        defaults={
            "crontab": schedule,
            "task": task,
            "args": json.dumps([id]),
            "one_off": True,
        }
    )
    
    return True

def cancel_task_by_name(task_name):
    deleted, _ = PeriodicTask.objects.filter(name=task_name).delete()
    
    return bool(deleted)