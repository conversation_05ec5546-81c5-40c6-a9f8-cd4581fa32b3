from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel


class InvoiceItem(BaseModel):
    invoice = models.ForeignKey(
        "billing.Invoice",
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name=_("Invoice")
    )
    description = models.CharField(
        max_length=255,
        verbose_name=_("Description"),
        help_text=_("A description of the item or service.")
    )
    quantity = models.PositiveIntegerField(
        default=1,
        verbose_name=_("Quantity"),
        help_text=_("The number of units for this item.")
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Unit Price"),
        help_text=_("The price per unit of the item.")
    )
    tax_rate = models.ForeignKey(
        "billing.TaxRate",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="invoice_items",
        verbose_name=_("Tax Rate")
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Total Price"),
        help_text=_("The total price for this item (quantity * unit price).")
    )

    class Meta:
        db_table = "billing_invoice_items"
        verbose_name = _("Invoice Item")
        verbose_name_plural = _("Invoice Items")

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.description} - {self.total_price} {self.invoice.currency_code}"