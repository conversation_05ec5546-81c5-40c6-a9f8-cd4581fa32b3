from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel


class TaxRate(BaseModel):
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Name"),
        help_text=_("The name of the tax (e.g., VAT, GST).")
    )
    rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_("Rate (%)"),
        help_text=_("The tax rate as a percentage (e.g., 20.00 for 20%).")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description"),
        help_text=_("Additional information about the tax rate.")
    )

    class Meta:
        db_table = "billing_tax_rates"
        verbose_name = _("Tax Rate")
        verbose_name_plural = _("Tax Rates")

    def __str__(self):
        return f"{self.name} ({self.rate}%)"