from django.db import models
from django.conf import settings
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.billing.constants import INVOICE_STATUS_CHOICES

User = settings.AUTH_USER_MODEL


class Invoice(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="invoices",
        verbose_name=_("User")
    )
    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_("Invoice Number"),
        help_text=_("A unique identifier for the invoice.")
    )
    status = models.CharField(
        max_length=20,
        choices=INVOICE_STATUS_CHOICES,
        default=INVOICE_STATUS_CHOICES[0][0],
        verbose_name=_("Status")
    )
    issue_date = models.DateField(
        verbose_name=_("Issue Date"),
        help_text=_("The date the invoice was issued.")
    )
    due_date = models.DateField(
        verbose_name=_("Due Date"),
        help_text=_("The date by which payment is due.")
    )
    currency_code = models.CharField(
        max_length=3,
        verbose_name=_("Currency Code"),
        help_text=_("ISO 4217 currency code (e.g., USD, EUR).")
    )
    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Subtotal"),
        help_text=_("The total amount before taxes.")
    )
    tax_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Tax Total"),
        help_text=_("The total tax amount.")
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Total Amount"),
        help_text=_("The final amount including taxes.")
    )
    notes = models.TextField(
        blank=True,
        verbose_name=_("Notes"),
        help_text=_("Additional notes or terms for the invoice.")
    )

    class Meta:
        db_table = "billing_invoices"
        verbose_name = _("Invoice")
        verbose_name_plural = _("Invoices")

    def __str__(self):
        return f"Invoice #{self.invoice_number} ({self.status})"