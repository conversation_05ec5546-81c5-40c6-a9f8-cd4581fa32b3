from django.contrib import admin
from dxh_common.admin import BaseAdmin

from apps.billing.models.invoice import Invoice
from apps.billing.models.invoice_item import InvoiceItem
from apps.billing.models.tax_rate import TaxRate


@admin.register(TaxRate)
class TaxRateAdmin(BaseAdmin):
    list_display = ('name', 'rate', 'description')
    search_fields = ('name',)


@admin.register(Invoice)
class InvoiceAdmin(BaseAdmin):
    list_display = (
        'invoice_number',
        'user',
        'status',
        'issue_date',
        'due_date',
        'currency_code',
        'total_amount'
    )
    list_filter = ('status', 'currency_code', 'issue_date', 'due_date')
    search_fields = ('invoice_number', 'user__username')
    date_hierarchy = 'issue_date'


@admin.register(InvoiceItem)
class InvoiceItemAdmin(BaseAdmin):
    list_display = (
        'invoice',
        'description',
        'quantity',
        'unit_price',
        'tax_rate',
        'total_price'
    )
    list_filter = ('invoice__invoice_number', 'tax_rate')
    search_fields = ('description',)
