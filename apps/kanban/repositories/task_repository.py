import datetime
from django.db.models import Q
from django.utils import timezone
from dxh_common.base.base_repository import BaseRepository, RepositoryError
from apps.kanban.models import Task


class TaskRepository(BaseRepository):
    def __init__(self):
        super().__init__(Task)
    
    def search_tasks(self, project_id, search_term):
        try:
            result = self.filter(
                Q(project_id=project_id) &
                Q(title__icontains=search_term) | 
                Q(description__icontains=search_term)
            )

            return result
        
        except Exception as e:
            raise RepositoryError(f"Error retrieving data: {e}")
    
    def get_tasks_due_soon(self, days=7):
        try:
            today = timezone.now()
            due_date = today + datetime.timedelta(days=days)
            
            tasks = self.filter(
                due_date__isnull=False,
                due_date__lte=due_date,
                status__in=['TODO', 'IN_PROGRESS']
            )

            return tasks
        
        except Exception as e:
            raise RepositoryError(f"Error retrieving data: {e}")
