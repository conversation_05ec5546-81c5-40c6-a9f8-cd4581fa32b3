from django.db.models import Q
from dxh_common.base.base_repository import BaseRepository, RepositoryError
from apps.kanban.models import Project, ProjectCategory, ProjectPermission


class ProjectRepository(BaseRepository):
    def __init__(self):
        super().__init__(Project)
    
    def search_projects(self, search_term):
        try:
            result = self.filter(
                Q(name__icontains=search_term) | 
                Q(description__icontains=search_term)
            )

            return result
        
        except Exception as e:
            raise RepositoryError(f"Error retrieving data: {e}")
    

class ProjectCategoryRepository(BaseRepository):
    def __init__(self):
        super().__init__(ProjectCategory)


class ProjectPermissionRepository(BaseRepository):
    def __init__(self):
        super().__init__(ProjectPermission)
