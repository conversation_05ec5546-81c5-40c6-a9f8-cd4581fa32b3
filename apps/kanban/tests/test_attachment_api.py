import tempfile
from rest_framework import status
from django.core.files.uploadedfile import SimpleUploadedFile
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import KanbanAttachment


class AttachmentAPITestCase(KanbanBaseTestCase):
    """Test suite for Attachment API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        
        # Create a test file
        self.test_file_content = b'Test file content'
        self.test_file = SimpleUploadedFile(
            name='test_file.txt',
            content=self.test_file_content,
            content_type='text/plain'
        )
        
        # Create a test attachment
        self.attachment = KanbanAttachment.objects.create(
            task=self.task1,
            file=self.test_file,
            file_name='test_file.txt',
            file_size=len(self.test_file_content),
            file_type='text/plain',
            uploaded_by=self.user,
            created_by=self.user,
            updated_by=self.user
        )
        
        # Define the API endpoints
        self.attachments_url = f"{self.base_url}attachments/"
        self.attachment_detail_url = f"{self.base_url}attachments/{self.attachment.id}/"

    def test_get_attachments_list(self):
        """Test retrieving a list of attachments"""
        response = self.client.get(f"{self.attachments_url}?task_id={self.task1.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Verify the attachment in the response is for the correct task
        self.assertEqual(response.data['data'][0]['task'], self.task1.id)
        self.assertEqual(response.data['data'][0]['file_name'], 'test_file.txt')

    def test_get_attachment_detail(self):
        """Test retrieving a single attachment"""
        response = self.client.get(self.attachment_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['task'], self.task1.id)
        self.assertEqual(response.data['data']['file_name'], 'test_file.txt')
        self.assertEqual(response.data['data']['file_type'], 'text/plain')

    def test_create_attachment(self):
        """Test creating a new attachment"""
        # Create a new test file
        new_test_file = SimpleUploadedFile(
            name='new_test_file.txt',
            content=b'New test file content',
            content_type='text/plain'
        )
        
        data = {
            'task': self.task1.id,
            'file': new_test_file,
            'file_name': 'new_test_file.txt',
            'file_size': len(b'New test file content'),
            'file_type': 'text/plain',
            'description': 'New test file description',
            'uploaded_by': self.user.id
        }
        
        response = self.client.post(self.attachments_url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['task'], self.task1.id)
        self.assertEqual(response.data['data']['file_name'], 'new_test_file.txt')
        self.assertEqual(response.data['data']['file_type'], 'text/plain')
        
        # Verify the attachment was created in the database
        self.assertTrue(KanbanAttachment.objects.filter(file_name='new_test_file.txt').exists())

    def test_update_attachment(self):
        """Test updating an attachment"""
        data = {
            'description': 'Updated test file description'
        }
        
        response = self.client.patch(self.attachment_detail_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['description'], 'Updated test file description')
        
        # Verify the attachment was updated in the database
        updated_attachment = KanbanAttachment.objects.get(id=self.attachment.id)
        self.assertEqual(updated_attachment.description, 'Updated test file description')

    def test_delete_attachment(self):
        """Test deleting an attachment"""
        response = self.client.delete(self.attachment_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify the attachment was deleted (or marked as deleted) in the database
        with self.assertRaises(KanbanAttachment.DoesNotExist):
            KanbanAttachment.objects.get(id=self.attachment.id, is_deleted=False)
