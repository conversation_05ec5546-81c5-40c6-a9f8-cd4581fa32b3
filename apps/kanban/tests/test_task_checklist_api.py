from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import TaskChecklistItem


class TaskChecklistAPITestCase(KanbanBaseTestCase):
    """Test suite for TaskChecklistItem API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        
        # Create test checklist items
        self.checklist_item1 = TaskChecklistItem.objects.create(
            task=self.task1,
            title='Checklist Item 1',
            is_completed=False,
            order=1,
            created_by=self.user,
            updated_by=self.user
        )
        
        self.checklist_item2 = TaskChecklistItem.objects.create(
            task=self.task1,
            title='Checklist Item 2',
            is_completed=True,
            order=2,
            created_by=self.user,
            updated_by=self.user
        )
        
        # Define the API endpoints
        self.checklist_items_url = f"{self.base_url}task-checklist-items/"
        self.checklist_item_detail_url = f"{self.base_url}task-checklist-items/{self.checklist_item1.id}/"
        self.checklist_item_toggle_url = f"{self.base_url}task-checklist-items/{self.checklist_item1.id}/toggle/"
        self.checklist_item_reorder_url = f"{self.base_url}task-checklist-items/reorder/"

    def test_get_checklist_items_list(self):
        """Test retrieving a list of checklist items"""
        response = self.client.get(f"{self.checklist_items_url}?task_id={self.task1.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Verify the response contains both checklist items
        self.assertEqual(len(response.data['data']), 2)
        
        # Verify the items are ordered correctly
        self.assertEqual(response.data['data'][0]['title'], 'Checklist Item 1')
        self.assertEqual(response.data['data'][0]['is_completed'], False)
        self.assertEqual(response.data['data'][1]['title'], 'Checklist Item 2')
        self.assertEqual(response.data['data'][1]['is_completed'], True)

    def test_get_checklist_item_detail(self):
        """Test retrieving a single checklist item"""
        response = self.client.get(self.checklist_item_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'Checklist Item 1')
        self.assertEqual(response.data['data']['is_completed'], False)
        self.assertEqual(response.data['data']['order'], 1)

    def test_create_checklist_item(self):
        """Test creating a new checklist item"""
        data = {
            'task': self.task1.id,
            'title': 'New Checklist Item',
            'is_completed': False,
            'order': 3
        }
        
        response = self.client.post(self.checklist_items_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'New Checklist Item')
        self.assertEqual(response.data['data']['is_completed'], False)
        self.assertEqual(response.data['data']['order'], 3)
        
        # Verify the checklist item was created in the database
        self.assertTrue(TaskChecklistItem.objects.filter(title='New Checklist Item').exists())

    def test_update_checklist_item(self):
        """Test updating a checklist item"""
        data = {
            'title': 'Updated Checklist Item 1'
        }
        
        response = self.client.patch(self.checklist_item_detail_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'Updated Checklist Item 1')
        
        # Verify the checklist item was updated in the database
        updated_item = TaskChecklistItem.objects.get(id=self.checklist_item1.id)
        self.assertEqual(updated_item.title, 'Updated Checklist Item 1')

    def test_toggle_checklist_item(self):
        """Test toggling a checklist item's completion status"""
        response = self.client.post(self.checklist_item_toggle_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the checklist item's completion status was toggled in the database
        updated_item = TaskChecklistItem.objects.get(id=self.checklist_item1.id)
        self.assertEqual(updated_item.is_completed, True)  # Was False before

    def test_reorder_checklist_items(self):
        """Test reordering checklist items"""
        data = {
            'items': [
                {'id': self.checklist_item1.id, 'order': 2},
                {'id': self.checklist_item2.id, 'order': 1}
            ]
        }
        
        response = self.client.post(self.checklist_item_reorder_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the checklist items were reordered in the database
        updated_item1 = TaskChecklistItem.objects.get(id=self.checklist_item1.id)
        updated_item2 = TaskChecklistItem.objects.get(id=self.checklist_item2.id)
        self.assertEqual(updated_item1.order, 2)  # Was 1 before
        self.assertEqual(updated_item2.order, 1)  # Was 2 before

    def test_delete_checklist_item(self):
        """Test deleting a checklist item"""
        response = self.client.delete(self.checklist_item_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify the checklist item was deleted in the database
        with self.assertRaises(TaskChecklistItem.DoesNotExist):
            TaskChecklistItem.objects.get(id=self.checklist_item1.id)
