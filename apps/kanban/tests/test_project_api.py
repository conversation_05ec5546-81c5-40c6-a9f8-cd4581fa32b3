from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import Project, ProjectCategory
from apps.kanban.constants import ProjectStatus, ProjectPriority


class ProjectAPITestCase(KanbanBaseTestCase):
    """Test suite for Project API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Define the API endpoints
        self.projects_url = f"{self.base_url}projects/"
        self.project_detail_url = f"{self.base_url}projects/{self.project.id}/"
        self.project_stakeholders_url = f"{self.base_url}projects/{self.project.id}/stakeholders/"
        self.project_categories_url = f"{self.base_url}project-categories/"
        self.project_category_detail_url = f"{self.base_url}project-categories/{self.project_category.id}/"
        self.project_permissions_url = f"{self.base_url}project-permissions/"

    def test_get_projects_list(self):
        """Test retrieving a list of projects"""
        response = self.client.get(f"{self.projects_url}?company_id={self.company.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('pagination', response.data)

        # Find the project with the name 'Test Project'
        test_project_found = False
        for project in response.data['data']:
            if project['name'] == 'Test Project':
                test_project_found = True
                self.assertEqual(project['description'], 'Test Project Description')
                self.assertEqual(project['status'], ProjectStatus.IN_PROGRESS)
                self.assertEqual(project['priority'], ProjectPriority.HIGH)
                break

        self.assertTrue(test_project_found, "Test Project not found in the response")

    def test_get_project_detail(self):
        """Test retrieving a single project"""
        response = self.client.get(self.project_detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Test Project')
        self.assertEqual(response.data['data']['description'], 'Test Project Description')
        self.assertEqual(response.data['data']['status'], ProjectStatus.IN_PROGRESS)
        self.assertEqual(response.data['data']['priority'], ProjectPriority.HIGH)

    def test_create_project(self):
        """Test creating a new project"""
        data = {
            'company': self.company.id,
            'category': self.project_category.id,
            'name': 'New Test Project',
            'slug': 'new-test-project',
            'description': 'New Test Project Description',
            'start_date': '2023-03-01',
            'status': ProjectStatus.NOT_STARTED,
            'priority': ProjectPriority.NORMAL,
            'type': 'SOFTWARE'
        }

        response = self.client.post(self.projects_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'New Test Project')
        self.assertEqual(response.data['data']['description'], 'New Test Project Description')

        # Verify the project was created in the database
        self.assertTrue(Project.objects.filter(name='New Test Project').exists())

    def test_update_project(self):
        """Test updating a project"""
        data = {
            'name': 'Updated Test Project',
            'description': 'Updated Test Project Description',
            'status': ProjectStatus.COMPLETED
        }

        response = self.client.put(self.project_detail_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Updated Test Project')
        self.assertEqual(response.data['data']['description'], 'Updated Test Project Description')
        self.assertEqual(response.data['data']['status'], ProjectStatus.COMPLETED)

        # Verify the project was updated in the database
        updated_project = Project.objects.get(id=self.project.id)
        self.assertEqual(updated_project.name, 'Updated Test Project')
        self.assertEqual(updated_project.description, 'Updated Test Project Description')
        self.assertEqual(updated_project.status, ProjectStatus.COMPLETED)

    def test_get_project_categories(self):
        """Test retrieving project categories"""
        response = self.client.get(f"{self.project_categories_url}?company_id={self.company.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Find the category with the name 'Test Category'
        test_category_found = False
        for category in response.data['data']:
            if category['name'] == 'Test Category':
                test_category_found = True
                self.assertEqual(category['description'], 'Test Category Description')
                self.assertEqual(category['color_code'], '#FF5733')
                break

        self.assertTrue(test_category_found, "Test Category not found in the response")

    def test_create_project_category(self):
        """Test creating a new project category"""
        data = {
            'company': self.company.id,
            'name': 'New Test Category',
            'description': 'New Test Category Description',
            'color_code': '#33FF57'
        }

        response = self.client.post(self.project_categories_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'New Test Category')
        self.assertEqual(response.data['data']['description'], 'New Test Category Description')
        self.assertEqual(response.data['data']['color_code'], '#33FF57')

        # Verify the category was created in the database
        self.assertTrue(ProjectCategory.objects.filter(name='New Test Category').exists())
