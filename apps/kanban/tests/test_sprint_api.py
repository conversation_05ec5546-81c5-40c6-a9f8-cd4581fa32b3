from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import Sprint
from apps.kanban.constants import SprintStatus


class SprintAPITestCase(KanbanBaseTestCase):
    """Test suite for Sprint API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Define the API endpoints
        self.sprints_url = f"{self.base_url}sprints/"
        self.sprint_detail_url = f"{self.base_url}sprints/{self.sprint.id}/"
        self.sprint_start_url = f"{self.base_url}sprints/{self.sprint.id}/start/"
        self.sprint_complete_url = f"{self.base_url}sprints/{self.sprint.id}/complete/"

    def test_get_sprints_list(self):
        """Test retrieving a list of sprints"""
        response = self.client.get(f"{self.sprints_url}?board_id={self.board.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Find the sprint with the name 'Sprint 1'
        sprint_found = False
        for sprint in response.data['data']:
            if sprint['name'] == 'Sprint 1':
                sprint_found = True
                self.assertEqual(sprint['board'], self.board.id)
                break

        self.assertTrue(sprint_found, "Sprint 1 not found in the response")

    def test_get_sprint_detail(self):
        """Test retrieving a single sprint"""
        response = self.client.get(self.sprint_detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Sprint 1')
        self.assertEqual(response.data['data']['board'], self.board.id)

    def test_create_sprint(self):
        """Test creating a new sprint"""
        data = {
            'board': self.board.id,
            'name': 'Sprint 2',
            'end_date': '2023-03-01'
        }

        response = self.client.post(self.sprints_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Sprint 2')
        self.assertEqual(response.data['data']['board'], self.board.id)

        # Verify the sprint was created in the database
        self.assertTrue(Sprint.objects.filter(name='Sprint 2').exists())

    def test_update_sprint(self):
        """Test updating a sprint"""
        data = {
            'name': 'Updated Sprint 1',
            'end_date': '2023-02-15'
        }

        response = self.client.put(self.sprint_detail_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Updated Sprint 1')

        # Verify the sprint was updated in the database
        updated_sprint = Sprint.objects.get(id=self.sprint.id)
        self.assertEqual(updated_sprint.name, 'Updated Sprint 1')

    def test_start_sprint(self):
        """Test starting a sprint"""
        response = self.client.post(self.sprint_start_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the sprint status was updated in the database
        updated_sprint = Sprint.objects.get(id=self.sprint.id)
        self.assertEqual(updated_sprint.status, SprintStatus.ACTIVE)

    def test_complete_sprint(self):
        """Test completing a sprint"""
        # First, start the sprint
        self.client.post(self.sprint_start_url)

        # Then complete it
        response = self.client.post(self.sprint_complete_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the sprint status was updated in the database
        updated_sprint = Sprint.objects.get(id=self.sprint.id)
        self.assertEqual(updated_sprint.status, SprintStatus.COMPLETED)
