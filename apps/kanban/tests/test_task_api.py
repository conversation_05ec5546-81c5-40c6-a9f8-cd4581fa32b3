from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import Task
from apps.kanban.constants import TaskStatus, TaskPriority, TaskType


class TaskAPITestCase(KanbanBaseTestCase):
    """Test suite for Task API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Define the API endpoints
        self.tasks_url = f"{self.base_url}tasks/"
        self.task_detail_url = f"{self.base_url}tasks/{self.task1.id}/"
        self.task_assign_url = f"{self.base_url}tasks/{self.task1.id}/assign/"
        self.task_status_url = f"{self.base_url}tasks/{self.task1.id}/status/"
        self.task_move_url = f"{self.base_url}tasks/{self.task1.id}/move/"

    def test_get_tasks_list(self):
        """Test retrieving a list of tasks"""
        response = self.client.get(self.tasks_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertIn('pagination', response.data)

        # Find the task with the title 'Task 1'
        task1_found = False
        for task in response.data['data']:
            if task['title'] == 'Task 1':
                task1_found = True
                self.assertEqual(task['description'], 'Task 1 Description')
                self.assertEqual(task['status'], TaskStatus.TODO)
                self.assertEqual(task['priority'], TaskPriority.HIGH)
                break

        self.assertTrue(task1_found, "Task 1 not found in the response")

    def test_get_tasks_by_project(self):
        """Test retrieving tasks by project"""
        response = self.client.get(f"{self.tasks_url}?project_id={self.project.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertGreaterEqual(len(response.data['data']), 2)  # Should have at least 2 tasks

    def test_get_tasks_by_board(self):
        """Test retrieving tasks by board"""
        response = self.client.get(f"{self.tasks_url}?board_id={self.board.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertGreaterEqual(len(response.data['data']), 2)  # Should have at least 2 tasks

    def test_get_tasks_by_sprint(self):
        """Test retrieving tasks by sprint"""
        response = self.client.get(f"{self.tasks_url}?sprint_id={self.sprint.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertGreaterEqual(len(response.data['data']), 1)  # Should have at least 1 task

    def test_get_tasks_by_column(self):
        """Test retrieving tasks by column"""
        response = self.client.get(f"{self.tasks_url}?column_id={self.column_todo.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertGreaterEqual(len(response.data['data']), 1)  # Should have at least 1 task

        # Verify the task in the response is in the To Do column
        self.assertEqual(response.data['data'][0]['title'], 'Task 1')

    def test_get_tasks_by_status(self):
        """Test retrieving tasks by status"""
        response = self.client.get(f"{self.tasks_url}?status={TaskStatus.TODO}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Verify all tasks in the response have the TODO status
        for task in response.data['data']:
            self.assertEqual(task['status'], TaskStatus.TODO)

    def test_get_task_detail(self):
        """Test retrieving a single task"""
        response = self.client.get(self.task_detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'Task 1')
        self.assertEqual(response.data['data']['description'], 'Task 1 Description')
        self.assertEqual(response.data['data']['status'], TaskStatus.TODO)
        self.assertEqual(response.data['data']['priority'], TaskPriority.HIGH)

    def test_create_task(self):
        """Test creating a new task"""
        data = {
            'project': self.project.id,
            'board': self.board.id,
            'column': self.column_todo.id,
            'title': 'New Test Task',
            'description': 'New Test Task Description',
            'type': TaskType.TASK,
            'status': TaskStatus.TODO,
            'priority': TaskPriority.NORMAL
        }

        response = self.client.post(self.tasks_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'New Test Task')
        self.assertEqual(response.data['data']['description'], 'New Test Task Description')

        # Verify the task was created in the database
        self.assertTrue(Task.objects.filter(title='New Test Task').exists())

    def test_update_task(self):
        """Test updating a task"""
        data = {
            'title': 'Updated Task 1',
            'description': 'Updated Task 1 Description',
            'status': TaskStatus.IN_PROGRESS
        }

        response = self.client.put(self.task_detail_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['title'], 'Updated Task 1')
        self.assertEqual(response.data['data']['description'], 'Updated Task 1 Description')
        self.assertEqual(response.data['data']['status'], TaskStatus.IN_PROGRESS)

        # Verify the task was updated in the database
        updated_task = Task.objects.get(id=self.task1.id)
        self.assertEqual(updated_task.title, 'Updated Task 1')
        self.assertEqual(updated_task.description, 'Updated Task 1 Description')
        self.assertEqual(updated_task.status, TaskStatus.IN_PROGRESS)

    def test_assign_task(self):
        """Test assigning a task to a user"""
        data = {
            'user_ids': [self.user.id]
        }

        response = self.client.post(self.task_assign_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the task was assigned to the user in the database
        updated_task = Task.objects.get(id=self.task1.id)
        self.assertIn(self.user, updated_task.assignees.all())

    def test_update_task_status(self):
        """Test updating a task's status"""
        data = {
            'status': TaskStatus.DONE
        }

        response = self.client.post(self.task_status_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the task status was updated in the database
        updated_task = Task.objects.get(id=self.task1.id)
        self.assertEqual(updated_task.status, TaskStatus.DONE)

    def test_move_task(self):
        """Test moving a task to a different column"""
        data = {
            'column_id': self.column_done.id
        }

        response = self.client.post(self.task_move_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the task was moved to the new column in the database
        updated_task = Task.objects.get(id=self.task1.id)
        self.assertEqual(updated_task.column.id, self.column_done.id)
