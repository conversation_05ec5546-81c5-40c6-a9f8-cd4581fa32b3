from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from apps.core.models import Company, SystemSetting
from apps.kanban.models import (
    Project, ProjectCategory, Board, BoardColumn, Task,
    Sprint, KanbanComment, KanbanAttachment, TaskDependency,
    CustomField, TaskTag
)
from apps.kanban.constants import (
    ProjectStatus, ProjectPriority, ProjectType,
    BoardType, BoardStatus, TaskStatus, TaskPriority,
    TaskType, TaskSeverity
)

User = get_user_model()


class KanbanBaseTestCase(APITestCase):
    """Base test case for Kanban API tests"""

    def setUp(self):
        """Set up test data"""
        # Create a test company
        self.company = Company.objects.create(
            name='Test Company',
            email='<EMAIL>',
            url='https://testcompany.com',
            mobile_no='1234567890'
        )

        # Create system settings required for pagination
        self.system_setting = SystemSetting.objects.create(
            company=self.company,
            app_name='Test App',
            app_version='1.0.0',
            default_date_format='DD/MM/YYYY',
            default_datetime_formate='DD/MM/YYYY HH:mm:ss',
            time_format='24-hour',
            default_currency='USD',
            default_language='en',
            default_timezone='UTC',
            pagination_size=10
        )

        # Create a test user
        self.user = User.objects.create(
            username='testuser',
            email='<EMAIL>',
            company=self.company,
            is_active=True,
            is_email_verified=True
        )
        self.user.set_password('testpassword123')
        self.user.save()

        # Create a project category
        self.project_category = ProjectCategory.objects.create(
            company=self.company,
            name='Test Category',
            description='Test Category Description',
            color_code='#FF5733'
        )

        # Create a project
        self.project = Project.objects.create(
            company=self.company,
            category=self.project_category,
            name='Test Project',
            slug='test-project',
            description='Test Project Description',
            start_date='2023-01-01',
            status=ProjectStatus.IN_PROGRESS,
            priority=ProjectPriority.HIGH,
            type=ProjectType.SOFTWARE,
            created_by=self.user,
            updated_by=self.user
        )

        # Create a board
        self.board = Board.objects.create(
            company=self.company,
            project=self.project,
            name='Test Board',
            description='Test Board Description',
            type=BoardType.KANBAN,
            status=BoardStatus.ACTIVE,
            created_by=self.user,
            updated_by=self.user
        )

        # Create board columns
        self.column_todo = BoardColumn.objects.create(
            board=self.board,
            name='To Do',
            order=1,
            created_by=self.user,
            updated_by=self.user
        )

        self.column_in_progress = BoardColumn.objects.create(
            board=self.board,
            name='In Progress',
            order=2,
            created_by=self.user,
            updated_by=self.user
        )

        self.column_done = BoardColumn.objects.create(
            board=self.board,
            name='Done',
            order=3,
            created_by=self.user,
            updated_by=self.user
        )

        # Create a sprint
        self.sprint = Sprint.objects.create(
            board=self.board,
            name='Sprint 1',
            end_date='2023-02-01',
            created_by=self.user,
            updated_by=self.user
        )

        # Create a task tag
        self.task_tag = TaskTag.objects.create(
            name='Bug',
            color_code='#FF0000'
        )

        # Create tasks
        self.task1 = Task.objects.create(
            project=self.project,
            board=self.board,
            column=self.column_todo,
            title='Task 1',
            description='Task 1 Description',
            type=TaskType.TASK,
            status=TaskStatus.TODO,
            priority=TaskPriority.HIGH,
            severity=TaskSeverity.MAJOR,
            sprint=self.sprint,
            created_by=self.user,
            updated_by=self.user
        )
        self.task1.tags.add(self.task_tag)

        self.task2 = Task.objects.create(
            project=self.project,
            board=self.board,
            column=self.column_in_progress,
            title='Task 2',
            description='Task 2 Description',
            type=TaskType.BUG,
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.NORMAL,
            severity=TaskSeverity.MINOR,
            created_by=self.user,
            updated_by=self.user
        )

        # Create a task dependency
        self.task_dependency = TaskDependency.objects.create(
            source_task=self.task1,
            target_task=self.task2,
            description='Task 1 blocks Task 2',
            created_by=self.user
        )

        # Create a comment
        self.comment = KanbanComment.objects.create(
            task=self.task1,
            author=self.user,
            content='Test comment',
            created_by=self.user,
            updated_by=self.user
        )

        # Create a custom field
        self.custom_field = CustomField.objects.create(
            project=self.project,
            name='Test Field',
            field_type='TEXT',
            is_required=False,
            created_by=self.user,
            updated_by=self.user
        )

        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Define the base API URL
        self.base_url = '/api/kanban/v1/'
