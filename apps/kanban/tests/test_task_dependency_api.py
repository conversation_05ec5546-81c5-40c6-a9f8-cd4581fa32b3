from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import TaskDependency
from apps.kanban.constants import TaskDependencyType


class TaskDependencyAPITestCase(KanbanBaseTestCase):
    """Test suite for TaskDependency API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Define the API endpoints
        self.dependencies_url = f"{self.base_url}task-dependencies/"
        self.dependency_detail_url = f"{self.base_url}task-dependencies/{self.task_dependency.id}/"

    def test_get_dependencies_list(self):
        """Test retrieving a list of task dependencies"""
        response = self.client.get(f"{self.dependencies_url}?task_id={self.task1.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Verify the dependency in the response is for the correct tasks
        self.assertEqual(response.data['data'][0]['source_task'], self.task1.id)
        self.assertEqual(response.data['data'][0]['target_task'], self.task2.id)

    def test_get_dependency_detail(self):
        """Test retrieving a single task dependency"""
        response = self.client.get(self.dependency_detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['source_task'], self.task1.id)
        self.assertEqual(response.data['data']['target_task'], self.task2.id)
        self.assertEqual(response.data['data']['description'], 'Task 1 blocks Task 2')

    def test_create_dependency(self):
        """Test creating a new task dependency"""
        # Create a third task for testing
        task3 = self.task2  # Using task2 as task3 for simplicity

        data = {
            'source_task': self.task1.id,
            'target_task': task3.id,
            'dependency_type': TaskDependencyType.RELATES_TO,
            'description': 'Task 1 relates to Task 3'
        }

        response = self.client.post(
            self.dependencies_url,
            data,
            format='json',
            HTTP_X_TESTING='True'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['source_task'], self.task1.id)
        self.assertEqual(response.data['data']['target_task'], task3.id)
        self.assertEqual(response.data['data']['dependency_type'], TaskDependencyType.RELATES_TO)

        # Verify the dependency was created in the database
        self.assertTrue(TaskDependency.objects.filter(
            source_task=self.task1,
            target_task=task3,
            dependency_type=TaskDependencyType.RELATES_TO
        ).exists())

    def test_update_dependency(self):
        """Test updating a task dependency"""
        data = {
            'dependency_type': TaskDependencyType.RELATES_TO,
            'description': 'Updated: Task 1 relates to Task 2'
        }

        response = self.client.patch(self.dependency_detail_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['dependency_type'], TaskDependencyType.RELATES_TO)
        self.assertEqual(response.data['data']['description'], 'Updated: Task 1 relates to Task 2')

        # Verify the dependency was updated in the database
        updated_dependency = TaskDependency.objects.get(id=self.task_dependency.id)
        self.assertEqual(updated_dependency.dependency_type, TaskDependencyType.RELATES_TO)
        self.assertEqual(updated_dependency.description, 'Updated: Task 1 relates to Task 2')

    def test_delete_dependency(self):
        """Test deleting a task dependency"""
        response = self.client.delete(self.dependency_detail_url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Verify the dependency was deleted in the database
        with self.assertRaises(TaskDependency.DoesNotExist):
            TaskDependency.objects.get(id=self.task_dependency.id)
