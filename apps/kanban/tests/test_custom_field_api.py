from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import CustomField, CustomFieldValue


class CustomFieldAPITestCase(KanbanBaseTestCase):
    """Test suite for CustomField API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Create a custom field value
        self.custom_field_value = CustomFieldValue.objects.create(
            task=self.task1,
            field=self.custom_field,
            value='Test value',
            created_by=self.user,
            updated_by=self.user
        )

        # Define the API endpoints
        self.custom_fields_url = f"{self.base_url}custom-fields/"
        self.custom_field_detail_url = f"{self.base_url}custom-fields/{self.custom_field.id}/"
        self.custom_field_values_url = f"{self.base_url}custom-field-values/"

    def test_get_custom_fields_list(self):
        """Test retrieving a list of custom fields"""
        response = self.client.get(f"{self.custom_fields_url}?project_id={self.project.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Find the custom field with the name 'Test Field'
        field_found = False
        for field in response.data['data']:
            if field['name'] == 'Test Field':
                field_found = True
                self.assertEqual(field['field_type'], 'TEXT')
                self.assertEqual(field['project'], self.project.id)
                break

        self.assertTrue(field_found, "Test Field not found in the response")

    def test_get_custom_field_detail(self):
        """Test retrieving a single custom field"""
        response = self.client.get(self.custom_field_detail_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Test Field')
        self.assertEqual(response.data['data']['field_type'], 'TEXT')
        self.assertEqual(response.data['data']['project'], self.project.id)

    def test_create_custom_field(self):
        """Test creating a new custom field"""
        data = {
            'project': self.project.id,
            'name': 'New Test Field',
            'field_type': 'NUMBER',
            'is_required': True,
            'default_value': '0'
        }

        response = self.client.post(
            self.custom_fields_url,
            data,
            format='json',
            HTTP_X_TESTING='True'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'New Test Field')
        self.assertEqual(response.data['data']['field_type'], 'NUMBER')
        self.assertEqual(response.data['data']['is_required'], True)

        # Verify the custom field was created in the database
        self.assertTrue(CustomField.objects.filter(name='New Test Field').exists())

    def test_update_custom_field(self):
        """Test updating a custom field"""
        data = {
            'name': 'Updated Test Field',
            'is_required': True
        }

        response = self.client.patch(self.custom_field_detail_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Updated Test Field')
        self.assertEqual(response.data['data']['is_required'], True)

        # Verify the custom field was updated in the database
        updated_field = CustomField.objects.get(id=self.custom_field.id)
        self.assertEqual(updated_field.name, 'Updated Test Field')
        self.assertEqual(updated_field.is_required, True)

    def test_get_custom_field_values(self):
        """Test retrieving custom field values for a task"""
        response = self.client.get(f"{self.custom_field_values_url}?task_id={self.task1.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)

        # Verify the custom field value in the response is for the correct task and field
        self.assertEqual(response.data['data'][0]['task'], self.task1.id)
        self.assertEqual(response.data['data'][0]['field'], self.custom_field.id)
        self.assertEqual(response.data['data'][0]['value'], 'Test value')

    def test_create_custom_field_value(self):
        """Test creating a new custom field value"""
        # First, create a new custom field
        new_field = CustomField.objects.create(
            project=self.project,
            name='Another Test Field',
            field_type='TEXT',
            created_by=self.user,
            updated_by=self.user
        )

        data = {
            'task': self.task1.id,
            'field': new_field.id,
            'value': 'Another test value'
        }

        response = self.client.post(self.custom_field_values_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['task'], self.task1.id)
        self.assertEqual(response.data['data']['field'], new_field.id)
        self.assertEqual(response.data['data']['value'], 'Another test value')

        # Verify the custom field value was created in the database
        self.assertTrue(CustomFieldValue.objects.filter(
            task=self.task1,
            field=new_field,
            value='Another test value'
        ).exists())

    def test_update_custom_field_value(self):
        """Test updating a custom field value"""
        data = {
            'task': self.task1.id,
            'field': self.custom_field.id,
            'value': 'Updated test value'
        }

        response = self.client.put(
            self.custom_field_values_url,
            data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify the custom field value was updated in the database
        updated_value = CustomFieldValue.objects.get(
            task=self.task1,
            field=self.custom_field
        )
        self.assertEqual(updated_value.value, 'Updated test value')
