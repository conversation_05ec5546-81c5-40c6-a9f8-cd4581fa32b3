from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase


class BoardAPITestCase(KanbanBaseTestCase):
    """Test suite for Board API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()

        # Define the API endpoints
        self.board_url = f"{self.base_url}board/{self.board.id}/"

    def test_get_board(self):
        """Test retrieving a board"""
        response = self.client.get(self.board_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['id'], self.board.id)
        self.assertEqual(response.data['data']['name'], 'Test Board')

    def test_get_nonexistent_board(self):
        """Test retrieving a non-existent board"""
        non_existent_board_url = f"{self.base_url}board/9999/"
        response = self.client.get(non_existent_board_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
