from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import ActivityLog
from apps.kanban.constants import ActivityType
from django.contrib.contenttypes.models import ContentType


class ActivityLogAPITestCase(KanbanBaseTestCase):
    """Test suite for ActivityLog API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        
        # Create an activity log
        task_content_type = ContentType.objects.get_for_model(self.task1)
        self.activity_log = ActivityLog.objects.create(
            project=self.project,
            user=self.user,
            activity_type=ActivityType.TASK_CREATED,
            description='Task 1 was created',
            content_type=task_content_type,
            object_id=self.task1.id,
            created_by=self.user,
            updated_by=self.user
        )
        
        # Define the API endpoints
        self.activity_logs_url = f"{self.base_url}activity-logs/"

    def test_get_activity_logs(self):
        """Test retrieving activity logs"""
        response = self.client.get(f"{self.activity_logs_url}?project_id={self.project.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Find the activity log with the description 'Task 1 was created'
        log_found = False
        for log in response.data['data']:
            if log['description'] == 'Task 1 was created':
                log_found = True
                self.assertEqual(log['activity_type'], ActivityType.TASK_CREATED)
                self.assertEqual(log['project'], self.project.id)
                self.assertEqual(log['user'], self.user.id)
                break
        
        self.assertTrue(log_found, "Activity log not found in the response")

    def test_get_activity_logs_by_user(self):
        """Test retrieving activity logs by user"""
        response = self.client.get(f"{self.activity_logs_url}?user_id={self.user.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Verify all logs in the response are for the correct user
        for log in response.data['data']:
            self.assertEqual(log['user'], self.user.id)

    def test_get_activity_logs_by_type(self):
        """Test retrieving activity logs by type"""
        response = self.client.get(f"{self.activity_logs_url}?activity_type={ActivityType.TASK_CREATED}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Verify all logs in the response have the correct activity type
        for log in response.data['data']:
            self.assertEqual(log['activity_type'], ActivityType.TASK_CREATED)
