from rest_framework import status
from apps.kanban.tests.test_base import KanbanBaseTestCase
from apps.kanban.models import KanbanComment


class CommentAPITestCase(KanbanBaseTestCase):
    """Test suite for Comment API views"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        
        # Define the API endpoints
        self.comments_url = f"{self.base_url}comments/"
        self.comment_detail_url = f"{self.base_url}comments/{self.comment.id}/"

    def test_get_comments_list(self):
        """Test retrieving a list of comments"""
        response = self.client.get(f"{self.comments_url}?task_id={self.task1.id}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # Verify the comment in the response is for the correct task
        self.assertEqual(response.data['data'][0]['task'], self.task1.id)
        self.assertEqual(response.data['data'][0]['content'], 'Test comment')

    def test_get_comment_detail(self):
        """Test retrieving a single comment"""
        response = self.client.get(self.comment_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['task'], self.task1.id)
        self.assertEqual(response.data['data']['content'], 'Test comment')

    def test_create_comment(self):
        """Test creating a new comment"""
        data = {
            'task': self.task1.id,
            'author': self.user.id,
            'content': 'New test comment'
        }
        
        response = self.client.post(self.comments_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['task'], self.task1.id)
        self.assertEqual(response.data['data']['content'], 'New test comment')
        
        # Verify the comment was created in the database
        self.assertTrue(KanbanComment.objects.filter(content='New test comment').exists())

    def test_update_comment(self):
        """Test updating a comment"""
        data = {
            'content': 'Updated test comment'
        }
        
        response = self.client.patch(self.comment_detail_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['content'], 'Updated test comment')
        
        # Verify the comment was updated in the database
        updated_comment = KanbanComment.objects.get(id=self.comment.id)
        self.assertEqual(updated_comment.content, 'Updated test comment')
        self.assertTrue(updated_comment.is_edited)

    def test_delete_comment(self):
        """Test deleting a comment"""
        response = self.client.delete(self.comment_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify the comment was deleted (or marked as deleted) in the database
        with self.assertRaises(KanbanComment.DoesNotExist):
            KanbanComment.objects.get(id=self.comment.id, is_deleted=False)
