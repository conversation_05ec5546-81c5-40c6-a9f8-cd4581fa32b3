from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination

from apps.kanban.services import ActivityLogService
from apps.kanban.api.v1.serializers import ActivityLogSerializer

logger = Logger(__name__)


class ActivityLogListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.activity_log_service = ActivityLogService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            project_id = request.query_params.get('project_id')
            user_id = request.query_params.get('user_id')
            activity_type = request.query_params.get('activity_type')
            
            if project_id:
                logs = self.activity_log_service.get(project_id=project_id)
            elif user_id:
                logs = self.activity_log_service.get(user_id=user_id)
            elif activity_type:
                logs = self.activity_log_service.get(activity_type=activity_type)
            else:
                logs = self.activity_log_service.get_all()
            
            paginated_data = self.paginator.paginate_queryset(logs, request)
            serializer = ActivityLogSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)
            
            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]
            
            result = {
                "message": _("Activity logs retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error({"event": "ActivityLogListView:get", "message": "Unexpected error", "error": str(e)})
            raise e
