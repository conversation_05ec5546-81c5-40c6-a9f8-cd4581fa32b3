from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status, MultiPartParser, FormParser
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.drf_yasg import swagger_auto_schema
from dxh_common.paginations import CustomPagination

from apps.kanban.services import AttachmentService
from apps.kanban.api.v1.serializers import AttachmentSerializer, AttachmentDetailSerializer

logger = Logger(__name__)


class AttachmentListView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.attachment_service = AttachmentService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            task_id = request.query_params.get('task_id')
            file_type = request.query_params.get('file_type')

            if not task_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"task_id": "Task ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if file_type:
                attachments = self.attachment_service.get(file_type=file_type,task_id=task_id)
            else:
                attachments = self.attachment_service.get(task_id=task_id)

            paginated_data = self.paginator.paginate_queryset(attachments, request)
            serializer = AttachmentDetailSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Attachments retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "AttachmentListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=AttachmentSerializer)
    def post(self, request):
        try:
            file = request.FILES.get('file')
            payload = request.data
            payload['file_name'] = file.name
            payload['file_size'] = file.size
            payload['file_type'] = file.content_type
            payload['uploaded_by'] = request.user.id

            serializer = AttachmentSerializer(data=payload)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            attachment = self.attachment_service.create(**validated_data)

            serializer = AttachmentSerializer(attachment)
            result = {
                "message": _("Attachment uploaded successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "AttachmentListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class AttachmentDetailView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.attachment_service = AttachmentService()

    def get(self, request, id):
        try:
            attachment = self.attachment_service.get(id=id)
            if not attachment:
                result = {
                    "message": _("No attachment found"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = AttachmentDetailSerializer(attachment)
            result = {
                "message": _("Attachment retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "AttachmentDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=AttachmentSerializer)
    def put(self, request, id):
        try:
            attachment = self.attachment_service.get(id=id)
            if not attachment:
                result = {
                    "message": _("No attachment found."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)
            
            if attachment.uploaded_by_id != request.user.id:
                result = {
                    "message": _("You can only edit your own attachments."),
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            file = request.FILES.get('file')
            payload = request.data
            payload['file_name'] = file.name
            payload['file_size'] = file.size
            payload['file_type'] = file.content_type
            payload['uploaded_by'] = request.user.id
            payload['updated_by'] = request.user.id
            serializer = AttachmentSerializer(attachment, data=payload, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            updated_attachment = self.attachment_service.update(attachment, **validated_data)
            serializer = AttachmentDetailSerializer(updated_attachment)
            result = {
                "message": _("Attachment updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "AttachmentDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=AttachmentSerializer)
    def patch(self, request, id):
        return self.put(request, id)

    def delete(self, request, id):
        try:
            attachment = self.attachment_service.get(id=id)
            if not attachment:
                result = {
                    "message": _("No attachment found"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            if attachment.uploaded_by_id != request.user.id:
                result = {
                    "message": _("You can only delete your own attachments."),
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            self.attachment_service.delete(attachment)
            result = {
                "message": _("Attachment deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "AttachmentDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e
