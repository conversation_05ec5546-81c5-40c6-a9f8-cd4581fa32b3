from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import CustomFieldService, CustomFieldValueService
from apps.kanban.api.v1.serializers import CustomFieldSerializer, CustomFieldValueSerializer

logger = Logger(__name__)


class CustomFieldListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.custom_field_service = CustomFieldService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            project_id = request.query_params.get('project_id')
            field_type = request.query_params.get('field_type')
            required_only = request.query_params.get('required_only', 'false').lower() == 'true'

            if not project_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"project_id": "Project ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if required_only:
                fields = self.custom_field_service.get(project_id=project_id, is_required=True)
            elif field_type:
                fields = self.custom_field_service.get(field_type=field_type, project_id=project_id)
            else:
                fields = self.custom_field_service.get(project_id=project_id)

            paginated_data = self.paginator.paginate_queryset(fields, request)
            serializer = CustomFieldSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Custom fields retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CustomFieldListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=CustomFieldSerializer)
    def post(self, request):
        try:
            payload = request.data
            serializer = CustomFieldSerializer(data=payload)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            project_id = validated_data['project'].id
            name = validated_data['name']

            existing = self.custom_field_service.get(project_id=project_id, name=name)
            if existing:
                serializer = CustomFieldSerializer(existing)
                result = {
                    "message": _("Custom field already exists"),
                    "data": serializer.data
                }
                return Response(result, status=status.HTTP_200_OK)

            field = self.custom_field_service.create(**validated_data)
            serializer = CustomFieldSerializer(field)
            result = {
                "message": _("Custom field created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "CustomFieldListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class CustomFieldDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.custom_field_service = CustomFieldService()

    def get(self, request, id):
        try:
            field = self.custom_field_service.get(id=id)
            if not field:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No custom field found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = CustomFieldSerializer(field)
            result = {
                "message": _("Custom field retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CustomFieldDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=CustomFieldSerializer)
    def put(self, request, id):
        try:
            field = self.custom_field_service.get(id=id)
            if not field:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No custom field found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            payload = request.data
            serializer = CustomFieldSerializer(field, data=payload, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_field = self.custom_field_service.update(field, **validated_data)
            serializer = CustomFieldSerializer(updated_field)
            result = {
                "message": _("Custom field updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CustomFieldDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            field = self.custom_field_service.get(id=id)
            if not field:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No custom field found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.custom_field_service.delete(field)

            result = {
                "message": _("Custom field deleted successfully")
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CustomFieldDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class CustomFieldValueView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.custom_field_service = CustomFieldValueService()

    def get(self, request):
        try:
            task_id = request.query_params.get('task_id')
            project_id = request.query_params.get('project_id')

            if not task_id:
                result = {
                    "message": _("Task ID is required."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if project_id:
                values = self.custom_field_service.list(task_id=task_id, project_id=project_id)
            else:
                values = self.custom_field_service.list(task_id=task_id)

            serializer = CustomFieldValueSerializer(values, many=True)
            result = {
                "message": _("Custom field values retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CustomFieldValueView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=CustomFieldValueSerializer)
    def post(self, request):
        try:
            payload = request.data
            serializer = CustomFieldValueSerializer(data=payload)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            task_id = validated_data['task'].id
            field_id = validated_data['field'].id
            value = validated_data['value']

            field_value = self.custom_field_service.set_field_value(
                task_id=task_id,
                field_id=field_id,
                value=value,
                user=request.user
            )
            serializer = CustomFieldValueSerializer(field_value)
            result = {
                "message": _("Custom field value set successfully"),
                "data": serializer.data
            }
            
            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "CustomFieldValueView:post", "message": "Unexpected error", "error": str(e)})
            raise e
