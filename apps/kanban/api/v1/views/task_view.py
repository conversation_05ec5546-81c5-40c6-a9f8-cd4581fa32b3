from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import TaskService, ProjectService
from apps.kanban.api.v1.serializers import TaskSerializer, TaskDetailSerializer

logger = Logger(__name__)


class TaskListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_service = TaskService()
        self.project_service = ProjectService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            project_id = request.query_params.get('project_id')
            if not project_id:
                result = {
                    "message": _("Project ID is required."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            project = self.project_service.get(id=project_id)
            if not project:
                result = {
                    "message": _("Project not found."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            board_id = request.query_params.get('board_id')
            sprint_id = request.query_params.get('sprint_id')
            column_id = request.query_params.get('column_id')
            task_status = request.query_params.get('status')
            priority = request.query_params.get('priority')
            task_type = request.query_params.get('type')
            search = request.query_params.get('search')

            if board_id:
                tasks = self.task_service.get_tasks_by_board(project_id, board_id)
            elif sprint_id:
                tasks = self.task_service.get_tasks_by_sprint(project_id, sprint_id)
            elif column_id:
                tasks = self.task_service.get_tasks_by_column(project_id, column_id)
            elif task_status:
                tasks = self.task_service.get_tasks_by_status(project_id, task_status)
            elif priority:
                tasks = self.task_service.get_tasks_by_priority(project_id, priority)
            elif task_type:
                tasks = self.task_service.get_tasks_by_type(project_id, task_type)
            elif search:
                tasks = self.task_service.search_tasks(project_id, search)
            else:
                tasks = self.task_service.list(project_id=project_id)

            paginated_data = self.paginator.paginate_queryset(tasks, request)
            serializer = TaskSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Tasks retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=TaskSerializer)
    def post(self, request):
        try:
            serializer = TaskSerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['created_by'] = request.user

            task = self.task_service.create_task(validated_data)
            serializer = TaskSerializer(task)
            result = {
                "message": _("Task created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "TaskListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class TaskDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_service = TaskService()

    def get(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("No task found"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskDetailSerializer(task)
            result = {
                "message": _("Task retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=TaskSerializer)
    def put(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No task found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskSerializer(task, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_task = self.task_service.update(task, **validated_data)

            serializer = TaskSerializer(updated_task)
            result = {
                "message": _("Task updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No task found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.task_service.delete(task)

            result = {
                "message": _("Task deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "TaskDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class TaskAssignView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_service = TaskService()

    @swagger_auto_schema(request_body=TaskSerializer)
    def post(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("No task found with ID {id}."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            user_ids = request.data.get('user_ids', [])
            if not user_ids:
                result = {
                    "message": _("User IDs are required."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            updated_task = self.task_service.assign_task(id, user_ids)
            serializer = TaskSerializer(updated_task)
            result = {
                "message": _("Task assigned successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskAssignView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class TaskStatusView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_service = TaskService()

    @swagger_auto_schema(request_body=TaskSerializer)
    def post(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("No task found with ID {id}."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            new_status = request.data.get('status')
            if not new_status:
                result = {
                    "message": _("Status is required."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            updated_task = self.task_service.change_task_status(id, new_status)

            serializer = TaskSerializer(updated_task)
            result = {
                "message": _("Task status updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskStatusView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class TaskMoveView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_service = TaskService()

    @swagger_auto_schema(request_body=TaskSerializer)
    def post(self, request, id):
        try:
            task = self.task_service.get(id=id)
            if not task:
                result = {
                    "message": _("No task found with ID {id}."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            column_id = request.data.get('column_id')
            if not column_id:
                result = {
                    "message": _("Column ID is required."),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            updated_task = self.task_service.move_task_to_column(id, column_id)
            serializer = TaskSerializer(updated_task)
            result = {
                "message": _("Task moved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskMoveView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e
