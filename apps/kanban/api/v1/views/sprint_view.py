from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import SprintService
from apps.kanban.api.v1.serializers import SprintSerializer, SprintDetailSerializer

logger = Logger(__name__)


class SprintListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sprint_service = SprintService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            board_id = request.query_params.get('board_id')
            sprint_status = request.query_params.get('status')
            active_only = request.query_params.get('active_only', 'false').lower() == 'true'
            upcoming_only = request.query_params.get('upcoming_only', 'false').lower() == 'true'

            if not board_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"board_id": "Board ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if active_only:
                sprint = self.sprint_service.get_active_sprint(board_id)
                if sprint:
                    sprints = [sprint]
                else:
                    sprints = []
            elif upcoming_only:
                sprints = self.sprint_service.get_upcoming_sprints(board_id)
            elif sprint_status:
                sprints = self.sprint_service.get_sprints_by_status(sprint_status).filter(board_id=board_id)
            else:
                sprints = self.sprint_service.get_sprints_by_board(board_id)

            paginated_data = self.paginator.paginate_queryset(sprints, request)
            serializer = SprintSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Sprints retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SprintListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=SprintSerializer)
    def post(self, request):
        try:
            serializer = SprintSerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['created_by'] = request.user
            validated_data['updated_by'] = request.user

            sprint = self.sprint_service.create_sprint(**validated_data)

            serializer = SprintSerializer(sprint)
            result = {
                "message": _("Sprint created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "SprintListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class SprintDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sprint_service = SprintService()

    def get(self, request, id):
        try:
            sprint = self.sprint_service.get(id=id)
            if not sprint:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No sprint found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = SprintDetailSerializer(sprint)
            result = {
                "message": _("Sprint retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SprintDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=SprintSerializer)
    def put(self, request, id):
        try:
            sprint = self.sprint_service.get(id=id)
            if not sprint:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No sprint found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = SprintSerializer(sprint, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_sprint = self.sprint_service.update_sprint(id, **validated_data)

            serializer = SprintSerializer(updated_sprint)
            result = {
                "message": _("Sprint updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SprintDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            sprint = self.sprint_service.get(id=id)
            if not sprint:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No sprint found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.sprint_service.delete(sprint)

            result = {
                "message": _("Sprint deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "SprintDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class SprintStartView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sprint_service = SprintService()

    @swagger_auto_schema(request_body=SprintSerializer)
    def post(self, request, id):
        try:
            sprint = self.sprint_service.get(id=id)
            if not sprint:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No sprint found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            updated_sprint = self.sprint_service.start_sprint(id, request.user.id)
            serializer = SprintSerializer(updated_sprint)
            result = {
                "message": _("Sprint started successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SprintStartView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class SprintCompleteView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.sprint_service = SprintService()

    @swagger_auto_schema(request_body=SprintSerializer)
    def post(self, request, id):
        try:
            sprint = self.sprint_service.get(id=id)
            if not sprint:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No sprint found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            updated_sprint = self.sprint_service.complete_sprint(id, request.user.id)

            serializer = SprintSerializer(updated_sprint)
            result = {
                "message": _("Sprint completed successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SprintCompleteView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e
