from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import TaskDependencyService
from apps.kanban.api.v1.serializers import TaskDependencySerializer


logger = Logger(__name__)


class TaskDependencyListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_dependency_service = TaskDependencyService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            task_id = request.query_params.get('task_id')
            dependency_type = request.query_params.get('dependency_type')
            direction = request.query_params.get('direction', 'outgoing')  # 'outgoing' or 'incoming'

            if not task_id:
                result = {
                    "message": _("Task ID is required."),
                    "errors": {"task_id": "Task ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if direction == 'incoming':
                dependencies = self.task_dependency_service.get_dependent_tasks(task_id)
            else:
                dependencies = self.task_dependency_service.get_dependencies_for_task(task_id)

            if dependency_type:
                dependencies = dependencies.filter(dependency_type=dependency_type)

            paginated_data = self.paginator.paginate_queryset(dependencies, request)
            serializer = TaskDependencySerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Task dependencies retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskDependencyListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=TaskDependencySerializer)
    def post(self, request):
        try:
            source_task = request.data.get('source_task')
            target_task = request.data.get('target_task')
            dependency_type = request.data.get('dependency_type')
            description = request.data.get('description', '')

            if not source_task or not target_task or not dependency_type:
                result = {
                    "message": _("Task ID is required."),
                    "errors": {"task_id": "Source task, target task, and dependency type are required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            else:
                dependency = self.task_dependency_service.create_dependency(
                    source_task_id=source_task,
                    target_task_id=target_task,
                    dependency_type=dependency_type,
                    description=description,
                    user_id=request.user.id
                )

            serializer = TaskDependencySerializer(dependency)
            result = {
                "message": _("Task dependency created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "TaskDependencyListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class TaskDependencyDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_dependency_service = TaskDependencyService()

    def get(self, request, id):
        try:
            dependency = self.task_dependency_service.get(id=id)
            if not dependency:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No task dependency found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskDependencySerializer(dependency)
            result = {
                "message": _("Task dependency retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskDependencyDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=TaskDependencySerializer)
    def put(self, request, id):
        try:
            dependency = self.task_dependency_service.get(id=id)
            if not dependency:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No task dependency found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskDependencySerializer(dependency, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_dependency = self.task_dependency_service.update(dependency, **validated_data)

            serializer = TaskDependencySerializer(updated_dependency)
            result = {
                "message": _("Task dependency updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskDependencyDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=TaskDependencySerializer)
    def patch(self, request, id):
        return self.put(request, id)

    def delete(self, request, id):
        try:
            dependency = self.task_dependency_service.get(id=id)
            if not dependency:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No task dependency found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.task_dependency_service.delete(dependency)

            result = {
                "message": _("Task dependency deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "TaskDependencyDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e
