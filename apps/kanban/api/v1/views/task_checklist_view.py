from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import TaskChecklistItemService
from apps.kanban.api.v1.serializers import TaskChecklistItemSerializer

logger = Logger(__name__)


class TaskChecklistItemListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_checklist_item_service = TaskChecklistItemService()

    def get(self, request):
        try:
            task_id = request.query_params.get('task_id')
            completed_only = request.query_params.get('completed_only', 'false').lower() == 'true'
            incomplete_only = request.query_params.get('incomplete_only', 'false').lower() == 'true'

            if not task_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"task_id": "Task ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if completed_only:
                items = self.task_checklist_item_service.get_completed_items(task_id)
            elif incomplete_only:
                items = self.task_checklist_item_service.get_incomplete_items(task_id)
            else:
                items = self.task_checklist_item_service.get_items_by_task(task_id)

            serializer = TaskChecklistItemSerializer(items, many=True)
            result = {
                "message": _("Checklist items retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=TaskChecklistItemSerializer)
    def post(self, request):
        try:
            serializer = TaskChecklistItemSerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['created_by'] = request.user
            validated_data['updated_by'] = request.user

            item = self.task_checklist_item_service.create_item(**validated_data)
            serializer = TaskChecklistItemSerializer(item)
            result = {
                "message": _("Checklist item created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class TaskChecklistItemDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_checklist_item_service = TaskChecklistItemService()

    def get(self, request, id):
        try:
            item = self.task_checklist_item_service.get(id=id)
            if not item:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No checklist item found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskChecklistItemSerializer(item)
            result = {
                "message": _("Checklist item retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def put(self, request, id):
        try:
            item = self.task_checklist_item_service.get(id=id)
            if not item:
                result ={
                    "message": _("Not found"),
                    "errors": {"id": f"No checklist item found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = TaskChecklistItemSerializer(item, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_item = self.task_checklist_item_service.update_item(id, **validated_data)

            serializer = TaskChecklistItemSerializer(updated_item)
            result = {
                "message": _("Checklist item updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def patch(self, request, id):
        return self.put(request, id)

    def delete(self, request, id):
        try:
            item = self.task_checklist_item_service.get(id=id)
            if not item:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No checklist item found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.task_checklist_item_service.delete(item)

            result = {
                "message": _("Checklist item deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class TaskChecklistItemToggleView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_checklist_item_service = TaskChecklistItemService()

    @swagger_auto_schema(request_body=TaskChecklistItemSerializer)
    def post(self, request, id):
        try:
            item = self.task_checklist_item_service.get(id=id)
            if not item:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No checklist item found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            updated_item = self.task_checklist_item_service.toggle_completion(id, request.user.id)

            serializer = TaskChecklistItemSerializer(updated_item)
            result = {
                "message": _("Checklist item toggled successfully"),
                "data": serializer.data
            }

            return Response(result , status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemToggleView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class TaskChecklistItemReorderView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.task_checklist_item_service = TaskChecklistItemService()

    @swagger_auto_schema(request_body=TaskChecklistItemSerializer)
    def post(self, request):
        try:
            items = request.data.get('items', [])

            if not items:
                result = {
                    "message": _("Bad request"),
                    "errors": {"items": "Items are required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            # Extract task_id from the first item
            item_ids = [item['id'] for item in items]
            first_item = self.task_checklist_item_service.get(id__in=item_ids)

            if not first_item:
                result = {
                    "message": _("Bad request"),
                    "errors": {"items": "No valid items found."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            task_id = first_item.task_id

            # Format the item_orders as expected by the service
            item_orders = [(item['id'], item['order']) for item in items]

            updated_items = self.task_checklist_item_service.reorder_items(task_id, item_orders, request.user.id)

            serializer = TaskChecklistItemSerializer(updated_items, many=True)
            result = {
                "message": _("Checklist items reordered successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TaskChecklistItemReorderView:post", "message": "Unexpected error", "error": str(e)})
            raise e
