from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.constants import BOARD_TYPE_CHOICES
from apps.kanban.services import (
    ProjectService, ProjectCategoryService, ProjectPermissionService, BoardService
)
from apps.kanban.api.v1.serializers import (
    ProjectSerializer, ProjectDetailSerializer, ProjectCategorySerializer, ProjectPermissionSerializer
)

logger = Logger(__name__)


class ProjectListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_service = ProjectService()
        self.board_service = BoardService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            company_id = request.query_params.get('company_id')
            category_id = request.query_params.get('category_id')
            project_status = request.query_params.get('status')
            team_id = request.query_params.get('team_id')
            search = request.query_params.get('search')
            active_only = request.query_params.get('active_only', 'false').lower() == 'true'

            if not company_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"company_id": "Company ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if category_id:
                projects = self.project_service.list(category_id=category_id)
            elif project_status:
                projects = self.project_service.list(project_status=project_status)
            elif team_id:
                projects = self.project_service.list(team_id=team_id)
            elif search:
                projects = self.project_service.search_projects(search)
            elif active_only:
                projects = self.project_service.get_active_projects()
            else:
                projects = self.project_service.list(company_id)

            paginated_data = self.paginator.paginate_queryset(projects, request)
            serializer = ProjectSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Projects retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=ProjectSerializer)
    def post(self, request):
        try:
            serializer = ProjectSerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['created_by'] = request.user
            validated_data['updated_by'] = request.user

            project = self.project_service.create_project(**validated_data)
            self.board_service.create(
                company= project.company, 
                project= project,
                name= "Board-1", 
                type = BOARD_TYPE_CHOICES[1][0]
            )

            serializer = ProjectSerializer(project)
            result = {
                "message": _("Project created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ProjectListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class ProjectDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_service = ProjectService()

    def get(self, request, id):
        try:
            project = self.project_service.get(id=id)
            if not project:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = ProjectDetailSerializer(project)
            result = {
                "message": _("Project retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=ProjectSerializer)
    def put(self, request, id):
        try:
            project = self.project_service.get(id=id)
            if not project:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = ProjectSerializer(project, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_project = self.project_service.update_project(id, **validated_data)
            serializer = ProjectSerializer(updated_project)
            result = {
                "message": _("Project updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            project = self.project_service.get(id=id)
            if not project:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.project_service.delete(project)
            result = {
                "message": _("Project deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "ProjectDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class ProjectStakeholderView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_service = ProjectService()

    def post(self, request, id):
        try:
            project = self.project_service.get(id=id)
            if not project:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            user_id = request.data.get('user_id')
            if not user_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"user_id": "User ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            updated_project = self.project_service.add_stakeholder(id, user_id)
            serializer = ProjectSerializer(updated_project)
            result = {
                "message": _("Stakeholder added successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectStakeholderView:post", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            project = self.project_service.get(id=id)
            if not project:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            user_id = request.query_params.get('user_id')
            if not user_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"user_id": "User ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            updated_project = self.project_service.remove_stakeholder(id, user_id)
            serializer = ProjectSerializer(updated_project)
            result = {
                "message": _("Stakeholder removed successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectStakeholderView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class ProjectCategoryListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_category_service = ProjectCategoryService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            company_id = request.query_params.get('company_id')

            if not company_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"company_id": "Company ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            categories = self.project_category_service.get_categories_by_company(company_id)

            paginated_data = self.paginator.paginate_queryset(categories, request)
            serializer = ProjectCategorySerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Project categories retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectCategoryListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=ProjectCategorySerializer)
    def post(self, request):
        try:
            serializer = ProjectCategorySerializer(data=request.data)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['created_by'] = request.user

            category = self.project_category_service.create(**validated_data)
            serializer = ProjectCategorySerializer(category)
            result = {
                "message": _("Project category created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ProjectCategoryListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class ProjectCategoryDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_category_service = ProjectCategoryService()

    def get(self, request, id):
        try:
            category = self.project_category_service.get(id=id)
            if not category:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project category found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = ProjectCategorySerializer(category)
            result = {
                "message": _("Project category retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectCategoryDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def put(self, request, id):
        try:
            category = self.project_category_service.get(id=id)
            if not category:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project category found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = ProjectCategorySerializer(category, data=request.data, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user

            updated_category = self.project_category_service.update_category(id, **validated_data)

            serializer = ProjectCategorySerializer(updated_category)
            result = {
                "message": _("Project category updated successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectCategoryDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    def delete(self, request, id):
        try:
            category = self.project_category_service.get(id=id)
            if not category:
                result = {
                    "message": _("Not found"),
                    "errors": {"id": f"No project category found with ID {id}."}
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.project_category_service.delete(category)

            result = {
                "message": _("Project category deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "ProjectCategoryDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e


class ProjectPermissionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.project_permission_service = ProjectPermissionService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            project_id = request.query_params.get('project_id')
            user_id = request.query_params.get('user_id')

            if not project_id and not user_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"project_id": "Project ID or user ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if project_id:
                permissions = self.project_permission_service.get_permissions_by_project(project_id)
            else:
                permissions = self.project_permission_service.get_permissions_by_user(user_id)

            # Paginate results
            paginated_data = self.paginator.paginate_queryset(permissions, request)
            serializer = ProjectPermissionSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Project permissions retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ProjectPermissionView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=ProjectPermissionSerializer)
    def post(self, request):
        try:
            project_id = request.data.get('project_id')
            user_id = request.data.get('user_id')
            level = request.data.get('level')

            if not project_id or not user_id or not level:
                result = {
                    "message": _("Bad request"),
                    "errors": {"project_id": "Project ID, user ID, and level are required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            permissions = {}
            for key in ['can_manage_tasks', 'can_manage_members', 'can_manage_sprints',
                       'can_view_timesheets', 'can_edit_project']:
                if key in request.data:
                    permissions[key] = request.data.get(key)

            permissions['created_by'] = request.user

            permission = self.project_permission_service.set_permission(project_id, user_id, level, **permissions)

            serializer = ProjectPermissionSerializer(permission)
            result = {
                "message": _("Project permission set successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ProjectPermissionView:post", "message": "Unexpected error", "error": str(e)})
            raise e

    def delete(self, request):
        try:
            project_id = request.query_params.get('project_id')
            user_id = request.query_params.get('user_id')

            if not project_id or not user_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"project_id": "Project ID and user ID are required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            success = self.project_permission_service.remove_permission(project_id, user_id)

            if success:
                result = {
                    "message": _("Project permission removed successfully")
                }
                return Response(result, status=status.HTTP_200_OK)

            result = {
                "message": _("Not found"),
                "errors": {"id": "No permission found for this project and user."}
            }

            return Response(result, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error({"event": "ProjectPermissionView:delete", "message": "Unexpected error", "error": str(e)})
            raise e
