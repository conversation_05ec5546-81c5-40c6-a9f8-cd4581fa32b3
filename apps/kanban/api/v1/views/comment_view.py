from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.kanban.services import CommentService
from apps.kanban.api.v1.serializers import CommentSerializer, CommentDetailSerializer, CommentListSerializer

logger = Logger(__name__)


class CommentListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.comment_service = CommentService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            task_id = request.query_params.get('task_id')
            parent_id = request.query_params.get('parent_id')

            if not task_id:
                result = {
                    "message": _("Bad request"),
                    "errors": {"task_id": "Task ID is required."}
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            if parent_id:
                comments = self.comment_service.get_replies_for_comment(parent_id)
            else:
                comments = self.comment_service.get_root_comments_by_task(task_id)

            paginated_data = self.paginator.paginate_queryset(comments, request)
            serializer = CommentListSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Comments retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentListView:get", "message": "Unexpected error", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=CommentSerializer)
    def post(self, request):
        try:
            payload = request.data
            payload['author'] = request.user.id
            serializer = CommentSerializer(data=payload)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            comment = self.comment_service.create(**validated_data)

            serializer = CommentSerializer(comment)
            result = {
                "message": _("Comment created successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "CommentListView:post", "message": "Unexpected error", "error": str(e)})
            raise e


class CommentDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.comment_service = CommentService()

    def get(self, request, id):
        try:
            comment = self.comment_service.get(id=id)
            if not comment:
                result = {
                    "message": _("No comment found."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = CommentDetailSerializer(comment)
            result = {
                "message": _("Comment retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentDetailView:get", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=CommentSerializer)
    def put(self, request, id):
        try:
            comment = self.comment_service.get(id=id)
            if not comment:
                result = {
                    "message": _("No comment found."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)
            
            if comment.author_id != request.user.id:
                result = {
                    "message": _("You can only edit your own comments."),
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)
            
            payload = request.data
            serializer = CommentSerializer(comment, data=payload, partial=True)
            if not serializer.is_valid():
                result = {
                    "message": _("Validation error"),
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            updated_comment = self.comment_service.update(comment, **validated_data)

            serializer = CommentDetailSerializer(updated_comment)
            result = {
                "message": _("Comment updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentDetailView:put", "message": "Unexpected error", "error": str(e), "id": id})
            raise e

    @swagger_auto_schema(request_body=CommentSerializer)
    def patch(self, request, id):
        return self.put(request, id)

    def delete(self, request, id):
        try:
            comment = self.comment_service.get(id=id)
            if not comment:
                result = {
                    "message": _("No comment found."),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            if comment.author_id != request.user.id:
                result = {
                    "message": _("You can only delete your own comments."),
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            self.comment_service.delete(comment)
            result = {
                "message": _("Comment deleted successfully")
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "CommentDetailView:delete", "message": "Unexpected error", "error": str(e), "id": id})
            raise e
