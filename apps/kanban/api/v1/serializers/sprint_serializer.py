from dxh_common.base.base_serializer import BaseModelSerializer
from apps.kanban.models import Sprint


class SprintSerializer(BaseModelSerializer):
    class Meta:
        model = Sprint
        fields = [
            "id", "board", "name", "end_date", "status", 
            "velocity", "capacity", "created_at", "updated_at"
        ]


class SprintDetailSerializer(BaseModelSerializer):
    class Meta:
        model = Sprint
        fields = [
            "id", "board", "name", "end_date", "status", 
            "velocity", "capacity", "created_at", "updated_at",
            "created_by", "updated_by"
        ]
