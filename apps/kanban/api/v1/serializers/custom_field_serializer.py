from dxh_common.base.base_serializer import BaseModelSerializer
from apps.kanban.models import CustomField, CustomFieldValue


class CustomFieldSerializer(BaseModelSerializer):
    class Meta:
        model = CustomField
        fields = [
            "id", "project", "name", "description", "field_type", 
            "is_required", "default_value", "options", "order", 
            "created_at", "updated_at"
        ]


class CustomFieldValueSerializer(BaseModelSerializer):
    class Meta:
        model = CustomFieldValue
        fields = [
            "id", "task", "field", "value", "updated_by", 
            "created_at", "updated_at"
        ]
