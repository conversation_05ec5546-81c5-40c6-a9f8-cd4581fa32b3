from dxh_common.base.base_serializer import BaseModelSerializer
from apps.kanban.models import Project, ProjectCategory, ProjectPermission


class ProjectCategorySerializer(BaseModelSerializer):
    class Meta:
        model = ProjectCategory
        fields = [
            "id", "company", "name", "description", "color_code",
            "created_at", "updated_at"
        ]


class ProjectPermissionSerializer(BaseModelSerializer):
    class Meta:
        model = ProjectPermission
        fields = [
            "id", "project", "user", "level", "can_manage_tasks",
            "can_manage_members", "can_manage_sprints", "can_view_timesheets",
            "can_edit_project", "created_at", "updated_at"
        ]


class ProjectSerializer(BaseModelSerializer):
    class Meta:
        model = Project
        fields = [
            "id", "company", "category", "name", "slug", "description",
            "start_date", "end_date", "actual_end_date", "estimated_hours", "stakeholders",
            "status", "priority", "type", "team", "created_at", "updated_at"
        ]


class ProjectDetailSerializer(BaseModelSerializer):
    category = ProjectCategorySerializer(read_only=True)
    permissions = ProjectPermissionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Project
        fields = [
            "id", "company", "category", "name", "slug", "description",
            "start_date", "end_date", "actual_end_date", "estimated_hours",
            "status", "priority", "type", "team", "stakeholders", "permissions",
            "created_at", "updated_at", "created_by", "updated_by"
        ]
