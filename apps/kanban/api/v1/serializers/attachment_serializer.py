from django.conf import settings
from dxh_common.base.base_serializer import BaseModelSerializer
from dxh_libraries.rest_framework import serializers
from apps.identity.api.v1.serializers.user_serializer import UserSerializer
from apps.kanban.models import KanbanAttachment, Task


class AttachmentSerializer(BaseModelSerializer):
    class Meta:
        model = KanbanAttachment
        fields = "__all__"
        read_only_fields = ["id", "created_at", "updated_at"]



class AttachmentDetailSerializer(BaseModelSerializer):
    task = serializers.PrimaryKeyRelatedField(queryset=Task.objects.all())
    uploaded_by = UserSerializer(read_only=True)
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = KanbanAttachment
        fields = [
            "id", "task", "file_url", "file_name", "file_size",
            "file_type", "description", "uploaded_by",
            "created_at", "updated_at", "created_by", "updated_by"
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Convert task to integer for consistent response
        if 'task' in representation and representation['task'] is not None:
            representation['task'] = int(representation['task'])
        return representation
    
    def get_file_url(self, obj):
        if obj.file:
            return settings.MEDIA_BASE_URL + obj.file.url
        
        return None
