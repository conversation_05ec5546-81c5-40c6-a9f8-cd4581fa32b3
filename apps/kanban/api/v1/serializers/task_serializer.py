from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer
from apps.user.api.v1.serializers import UserSerializer
from apps.kanban.api.v1.serializers.comment_serializer import CommentSerializer
from apps.kanban.models import Task, TaskTag


class TaskTagSerializer(BaseModelSerializer):
    class Meta:
        model = TaskTag
        fields = ["id", "name", "color_code"]


class TaskSerializer(BaseModelSerializer):
    tags = TaskTagSerializer(many=True, read_only=True)
    
    class Meta:
        model = Task
        fields = [
            "id", "title", "description", "type", "status", "priority", 
            "severity", "resolution", "estimate", "estimate_unit", 
            "time_spent", "time_remaining", "due_date", "completed_at", 
            "started_at", "is_blocked", "blocked_reason", "rank", 
            "project", "board", "column", "parent_task", "sprint", 
            "tags", "created_at", "updated_at"
        ]
        read_only_fields = [
            "id", "created_at", "updated_at"
        ]


class TaskDetailSerializer(BaseModelSerializer):
    tags = TaskTagSerializer(many=True, read_only=True)
    comments = serializers.SerializerMethodField()
    
    class Meta:
        model = Task
        fields = [
            "id", "title", "description", "type", "status", "priority", 
            "severity", "resolution", "estimate", "estimate_unit", 
            "time_spent", "time_remaining", "original_estimate", 
            "due_date", "completed_at", "started_at", "is_blocked", 
            "blocked_reason", "rank", "project", "board", "column", 
            "parent_task", "sprint", "tags", 
            "assignees", "watchers", "comments",
            "created_at", "updated_at", "created_by", "updated_by"
        ]
        read_only_fields = [
            "id", "created_at", "updated_at", "created_by", "updated_by", "comments"
        ]
    
    def get_comments(self, obj):
        comments = obj.comments.all().order_by('-created_at')
        return CommentSerializer(comments, many=True).data
    
    def get_assignees(self, obj):
        return UserSerializer(obj.assignees, many=True).data
    
    def get_watchers(self, obj):
        return UserSerializer(obj.watchers, many=True).data
