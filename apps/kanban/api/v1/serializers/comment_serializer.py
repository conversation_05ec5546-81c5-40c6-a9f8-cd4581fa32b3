from dxh_common.base.base_serializer import BaseModelSerializer
from dxh_libraries.rest_framework import serializers
from apps.identity.api.v1.serializers.user_serializer import UserSerializer
from apps.kanban.models import KanbanComment, Task
from django.contrib.auth import get_user_model

User = get_user_model()


class CommentSerializer(BaseModelSerializer):
    class Meta:
        model = KanbanComment
        fields = "__all__"
        read_only_fields = ["id", "created_at", "updated_at"]


class CommentListSerializer(BaseModelSerializer):
    author = UserSerializer(read_only=True)

    class Meta:
        model = KanbanComment
        fields = [
            "id", "task", "author", "content", "is_edited",
            "edited_at", "parent_comment", "created_at", "updated_at"
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class CommentDetailSerializer(BaseModelSerializer):
    task = serializers.PrimaryKeyRelatedField(queryset=Task.objects.all())
    author = UserSerializer(read_only=True)

    class Meta:
        model = KanbanComment
        fields = [
            "id", "task", "author", "content", "is_edited",
            "edited_at", "parent_comment", "created_at", "updated_at",
            "created_by", "updated_by"
        ]
        read_only_fields = ["id", "created_at", "updated_at", "created_by", "updated_by"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if 'task' in representation and representation['task'] is not None:
            representation['task'] = int(representation['task'])

        return representation
