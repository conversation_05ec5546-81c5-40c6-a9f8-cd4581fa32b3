# Generated by Django 5.1.5 on 2025-05-13 08:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0010_filetype_company_language_company'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TaskTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Tag Name')),
                ('color_code', models.CharField(default='#3498db', max_length=7, verbose_name='Color Code')),
            ],
            options={
                'verbose_name': 'Task Tag',
                'verbose_name_plural': 'Task Tags',
                'db_table': 'kanban_task_tags',
            },
        ),
        migrations.CreateModel(
            name='Board',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, verbose_name='Board Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Board Description')),
                ('type', models.CharField(choices=[('scrum', 'Scrum'), ('kanban', 'Kanban'), ('calendar', 'Calendar'), ('list', 'List View')], default='kanban', max_length=20, verbose_name='Board Type')),
                ('status', models.CharField(choices=[('active', 'Active'), ('archived', 'Archived'), ('template', 'Template')], default='active', max_length=20, verbose_name='Board Status')),
                ('is_template', models.BooleanField(default=False, verbose_name='Is Template')),
                ('template_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Template Name')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kanban_bords', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Board',
                'verbose_name_plural': 'Boards',
                'db_table': 'kanban_boards',
            },
        ),
        migrations.CreateModel(
            name='BoardColumn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=50, verbose_name='Column Name')),
                ('order', models.IntegerField(default=0, verbose_name='Column Order')),
                ('wip_limit', models.PositiveIntegerField(blank=True, help_text='Maximum number of items allowed in this column at once.', null=True, verbose_name='WIP Limit')),
                ('status', models.CharField(choices=[('active', 'Active'), ('archived', 'Archived'), ('hidden', 'Hidden')], default='active', max_length=20, verbose_name='Column Status')),
                ('color_code', models.CharField(default='#3498db', max_length=7, verbose_name='Color Code')),
                ('board', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='columns', to='kanban.board', verbose_name='Board')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Board Column',
                'verbose_name_plural': 'Board Columns',
                'db_table': 'kanban_board_columns',
            },
        ),
        migrations.CreateModel(
            name='CompanyMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('admin', 'Administrator'), ('member', 'Member'), ('guest', 'Guest')], default='member', max_length=20, verbose_name='Role')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is Primary')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_memberships', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Company Member',
                'verbose_name_plural': 'Company Members',
                'db_table': 'kanban_company_members',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, verbose_name='Department Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Department Description')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('head', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL, verbose_name='Department Head')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
                'db_table': 'kanban_departments',
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=255, verbose_name='Project Name')),
                ('slug', models.SlugField(max_length=255, unique=True, verbose_name='Project Slug')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Project Description')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('actual_end_date', models.DateField(blank=True, null=True, verbose_name='Actual End Date')),
                ('estimated_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Estimated Hours')),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('planning', 'Planning'), ('in_progress', 'In Progress'), ('on_hold', 'On Hold'), ('completed', 'Completed'), ('archived', 'Archived')], default='not_started', max_length=20, verbose_name='Project Status')),
                ('priority', models.CharField(choices=[('normal', 'Normal'), ('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=20, verbose_name='Project Priority')),
                ('type', models.CharField(choices=[('software', 'Software Development'), ('design', 'Design'), ('macketing', 'Marketing'), ('research', 'Research & Development'), ('customer_support', 'Customer Support'), ('general', 'General Business')], default='software', max_length=20, verbose_name='Project Type')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('stakeholders', models.ManyToManyField(blank=True, related_name='stakeholder_projects', to=settings.AUTH_USER_MODEL, verbose_name='Stakeholders')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Project',
                'verbose_name_plural': 'Projects',
                'db_table': 'kanban_projects',
            },
        ),
        migrations.CreateModel(
            name='CustomField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, verbose_name='Field Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('field_type', models.CharField(choices=[('text', 'Text'), ('number', 'Number'), ('date', 'Date'), ('boolean', 'Boolean'), ('select', 'Select'), ('multi_select', 'Multi Select'), ('user', 'User'), ('url', 'URL')], default='text', max_length=20, verbose_name='Field Type')),
                ('is_required', models.BooleanField(default=False, verbose_name='Is Required')),
                ('default_value', models.TextField(blank=True, null=True, verbose_name='Default Value')),
                ('options', models.JSONField(blank=True, null=True, verbose_name='Options')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Display Order')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_fields', to='kanban.project', verbose_name='Project')),
            ],
            options={
                'verbose_name': 'Custom Field',
                'verbose_name_plural': 'Custom Fields',
                'db_table': 'kanban_custom_fields',
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='board',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='boards', to='kanban.project', verbose_name='Project'),
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('activity_type', models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('deleted', 'Deleted'), ('commented', 'Commented'), ('status_changed', 'Status Changed'), ('assigned', 'Assigned'), ('unassigned', 'Unassigned'), ('moved', 'Moved'), ('attachment_added', 'Attachment Added'), ('attachment_removed', 'Attachment Removed'), ('sprint_added', 'Added to Sprint'), ('sprint_removed', 'Removed from Sprint'), ('task_created', 'Task Created'), ('task_updated', 'Task Updated'), ('task_deleted', 'Task Deleted')], max_length=30, verbose_name='Activity Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('content_type', models.CharField(blank=True, max_length=255, null=True, verbose_name='Content Type')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='Object ID')),
                ('metadata', models.JSONField(blank=True, null=True, verbose_name='Additional Data')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kanban_activity_logs', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kanban_activity_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_logs', to='kanban.project', verbose_name='Project')),
            ],
            options={
                'verbose_name': 'Activity Log',
                'verbose_name_plural': 'Activity Logs',
                'db_table': 'kanban_activity_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Category Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Category Description')),
                ('color_code', models.CharField(default='#3498db', max_length=7, verbose_name='Color Code')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='company_project_categories', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Project Category',
                'verbose_name_plural': 'Project Categories',
                'db_table': 'kanban_project_categories',
            },
        ),
        migrations.AddField(
            model_name='project',
            name='category',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='kanban.projectcategory', verbose_name='Category'),
        ),
        migrations.CreateModel(
            name='ProjectPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('level', models.CharField(choices=[('admin', 'Admin'), ('task_manager', 'Task Manager'), ('member', 'Member'), ('observer', 'Observer')], default='member', max_length=20, verbose_name='Permission Level')),
                ('can_manage_tasks', models.BooleanField(default=True, verbose_name='Can Manage Tasks')),
                ('can_manage_members', models.BooleanField(default=False, verbose_name='Can Manage Members')),
                ('can_manage_sprints', models.BooleanField(default=False, verbose_name='Can Manage Sprints')),
                ('can_view_timesheets', models.BooleanField(default=True, verbose_name='Can View Timesheets')),
                ('can_edit_project', models.BooleanField(default=False, verbose_name='Can Edit Project')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to='kanban.project', verbose_name='Project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_permissions', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Project Permission',
                'verbose_name_plural': 'Project Permissions',
                'db_table': 'kanban_project_permissions',
            },
        ),
        migrations.CreateModel(
            name='Sprint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, verbose_name='Sprint Name')),
                ('end_date', models.DateTimeField(verbose_name='End Date')),
                ('status', models.CharField(choices=[('planning', 'Planning'), ('active', 'Active'), ('completed', 'Completed'), ('archived', 'Archived')], default='planning', max_length=20, verbose_name='Sprint Status')),
                ('velocity', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Velocity')),
                ('capacity', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Capacity')),
                ('board', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sprints', to='kanban.board', verbose_name='Board')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Sprint',
                'verbose_name_plural': 'Sprints',
                'db_table': 'kanban_sprints',
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('title', models.CharField(max_length=255, verbose_name='Task Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Task Description')),
                ('type', models.CharField(choices=[('user_story', 'User Story'), ('bug', 'Bug'), ('task', 'Task'), ('feature', 'Feature'), ('epic', 'Epic'), ('chore', 'Chore'), ('doc', 'Documentation')], default='task', max_length=20, verbose_name='Task Type')),
                ('status', models.CharField(choices=[('todo', 'To Do'), ('in_progress', 'In Progress'), ('code_reviewed', 'Code Reviewed'), ('testing', 'Testing'), ('done', 'Done'), ('blocked', 'Blocked'), ('canceled', 'Canceled')], default='todo', max_length=20, verbose_name='Task Status')),
                ('priority', models.CharField(choices=[('lowest', 'Lowest'), ('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent'), ('critical', 'Critical')], default='normal', max_length=20, verbose_name='Task Priority')),
                ('severity', models.CharField(choices=[('trivial', 'Trivial'), ('minor', 'Minor'), ('major', 'Major'), ('critical', 'Critical'), ('blocker', 'Blocker')], default='major', max_length=20, verbose_name='Task Severity')),
                ('resolution', models.CharField(blank=True, choices=[('fixed', 'Fixed'), ('wont_fix', "Won't Fix"), ('duplicate', 'Duplicate'), ('invalid', 'Invalid'), ('works_for_me', 'Works For Me'), ('unresolved', 'Unresolved')], default='unresolved', max_length=20, null=True, verbose_name='Task Resolution')),
                ('estimate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Estimated Hours')),
                ('estimate_unit', models.CharField(choices=[('hour', 'Hour'), ('day', 'Day'), ('week', 'Week')], default='hour', max_length=20, verbose_name='Estimate Unit')),
                ('time_spent', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Time Spent')),
                ('time_remaining', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Remaining Time')),
                ('original_estimate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Original Estimate')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='Due Date')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='Started At')),
                ('rank', models.IntegerField(default=0, help_text='Used for ordering tasks within a column.', verbose_name='Rank')),
                ('is_blocked', models.BooleanField(default=False, verbose_name='Is Blocked')),
                ('blocked_reason', models.TextField(blank=True, null=True, verbose_name='Blocked Reason')),
                ('assignees', models.ManyToManyField(blank=True, related_name='assigned_tasks', to=settings.AUTH_USER_MODEL, verbose_name='Assignees')),
                ('board', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tasks', to='kanban.board', verbose_name='Board')),
                ('column', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tasks', to='kanban.boardcolumn', verbose_name='Column')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('parent_task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subtasks', to='kanban.task', verbose_name='Parent Task')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='kanban.project', verbose_name='Project')),
                ('sprint', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tasks', to='kanban.sprint', verbose_name='Sprint')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('watchers', models.ManyToManyField(blank=True, related_name='watched_tasks', to=settings.AUTH_USER_MODEL, verbose_name='Watchers')),
                ('tags', models.ManyToManyField(blank=True, related_name='tasks', to='kanban.tasktag', verbose_name='Tags')),
            ],
            options={
                'verbose_name': 'Task',
                'verbose_name_plural': 'Tasks',
                'db_table': 'kanban_tasks',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KanbanComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('content', models.TextField(verbose_name='Comment Content')),
                ('is_edited', models.BooleanField(default=False, verbose_name='Is Edited')),
                ('edited_at', models.DateTimeField(blank=True, null=True, verbose_name='Edited At')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_comments', to=settings.AUTH_USER_MODEL, verbose_name='Author')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('parent_comment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='kanban.kanbancomment', verbose_name='Parent Comment')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kanban_comments', to='kanban.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Kanban Comment',
                'verbose_name_plural': 'Kanban Comments',
                'db_table': 'kanban_comments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KanbanAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('file', models.FileField(upload_to='kanban/attachments/%Y/%m/%d/', verbose_name='File')),
                ('file_name', models.CharField(max_length=255, verbose_name='File Name')),
                ('file_size', models.PositiveIntegerField(verbose_name='File Size (bytes)')),
                ('file_type', models.CharField(max_length=100, verbose_name='File Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kanban_attachments', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_attachments', to=settings.AUTH_USER_MODEL, verbose_name='Uploaded By')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='kanban.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Kanban Attachment',
                'verbose_name_plural': 'Kanban Attachments',
                'db_table': 'kanban_attachments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomFieldValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('value', models.TextField(blank=True, null=True, verbose_name='Field Value')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='kanban.customfield', verbose_name='Custom Field')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_custom_field_values', to=settings.AUTH_USER_MODEL, verbose_name='Updated By')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_field_values', to='kanban.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Custom Field Value',
                'verbose_name_plural': 'Custom Field Values',
                'db_table': 'kanban_custom_field_values',
            },
        ),
        migrations.CreateModel(
            name='TaskChecklistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Checklist Item Description')),
                ('is_completed', models.BooleanField(default=False, verbose_name='Is Completed')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Order')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_checklists', to=settings.AUTH_USER_MODEL, verbose_name='Completed By')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checklist_items', to='kanban.task', verbose_name='Task')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Task Checklist Item',
                'verbose_name_plural': 'Task Checklist Items',
                'db_table': 'kanban_task_checklist_items',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='TaskDependency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('dependency_type', models.CharField(choices=[('block', 'Block'), ('relate', 'Relate'), ('duplicate', 'Duplicate'), ('test', 'Test'), ('implement', 'Implement'), ('relates_to', 'Relates To')], default='block', max_length=20, verbose_name='Dependency Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_dependencies', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('source_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependencies', to='kanban.task', verbose_name='Source Task')),
                ('target_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependent_tasks', to='kanban.task', verbose_name='Target Task')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Task Dependency',
                'verbose_name_plural': 'Task Dependencies',
                'db_table': 'kanban_task_dependencies',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('name', models.CharField(max_length=100, verbose_name='Team Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Team Description')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teams', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='teams', to='kanban.department', verbose_name='Department')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Team',
                'verbose_name_plural': 'Teams',
                'db_table': 'kanban_teams',
            },
        ),
        migrations.AddField(
            model_name='project',
            name='team',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='kanban.team', verbose_name='Team'),
        ),
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('role', models.CharField(choices=[('lead', 'Team Lead'), ('developer', 'Developer'), ('designer', 'Designer'), ('qa_engineer', 'QA Engineer'), ('observer', 'Observer'), ('analyst', 'Analyst')], default='developer', max_length=20, verbose_name='Role')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='team_members', to='kanban.team', verbose_name='Team')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kanban_team_member_roles', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Team Member',
                'verbose_name_plural': 'Team Members',
                'db_table': 'kanban_team_members',
            },
        ),
        migrations.AddField(
            model_name='team',
            name='members',
            field=models.ManyToManyField(related_name='kanban_team_memberships', through='kanban.TeamMember', to=settings.AUTH_USER_MODEL, verbose_name='Team Members'),
        ),
        migrations.AddConstraint(
            model_name='boardcolumn',
            constraint=models.UniqueConstraint(fields=('board', 'order'), name='unique_board_column_order'),
        ),
        migrations.AlterUniqueTogether(
            name='companymember',
            unique_together={('company', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('company', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='customfield',
            unique_together={('project', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='projectcategory',
            unique_together={('company', 'name')},
        ),
        migrations.AlterUniqueTogether(
            name='projectpermission',
            unique_together={('project', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='customfieldvalue',
            unique_together={('task', 'field')},
        ),
        migrations.AlterUniqueTogether(
            name='taskdependency',
            unique_together={('source_task', 'target_task')},
        ),
        migrations.AlterUniqueTogether(
            name='project',
            unique_together={('company', 'slug')},
        ),
        migrations.AlterUniqueTogether(
            name='teammember',
            unique_together={('team', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='team',
            unique_together={('company', 'name')},
        ),
    ]
