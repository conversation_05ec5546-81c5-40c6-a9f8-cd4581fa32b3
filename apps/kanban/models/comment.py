from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

User = get_user_model()


class KanbanComment(BaseModel):
    task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="kanban_comments",
        verbose_name=_("Task")
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="task_comments",
        verbose_name=_("Author")
    )
    content = models.TextField(
        verbose_name=_("Comment Content")
    )
    is_edited = models.BooleanField(
        default=False,
        verbose_name=_("Is Edited")
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Edited At")
    )
    parent_comment = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="replies",
        verbose_name=_("Parent Comment")
    )

    class Meta:
        db_table = "kanban_comments"
        verbose_name = _("Kanban Comment")
        verbose_name_plural = _("Kanban Comments")
        ordering = ['-created_at']

    def __str__(self):
        return f"Comment by {self.author} on {self.task}"
