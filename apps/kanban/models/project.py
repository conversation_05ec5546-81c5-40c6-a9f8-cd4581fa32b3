from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import (
    BLUE_COLOR, PROJECT_PRIORITY_CHOICES, PROJECT_STATUS_CHOICES, PROJECT_TYPE_CHOICES, PROJECT_PERMISSION_LEVEL_CHOICES
)

User = get_user_model()


class ProjectCategory(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        related_name="company_project_categories",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_("Category Name")
    )
    description = models.TextField(
        blank=True, 
        null=True,
        verbose_name=_("Category Description")
    )
    color_code = models.CharField(
        max_length=7, 
        default=BLUE_COLOR,
        verbose_name=_("Color Code")
    )  

    class Meta:
        db_table = "kanban_project_categories"
        unique_together = ['company', 'name']
        verbose_name = _("Project Category")
        verbose_name_plural = _("Project Categories")

    def __str__(self):
        return f"{self.company}/{self.name}"
    

class Project(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        related_name="projects",
        verbose_name=_("Company")
    )
    category = models.ForeignKey(
        ProjectCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name="projects",
        verbose_name=_("Category")
    )
    name = models.CharField(
        max_length=255,
        verbose_name=_("Project Name")
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        verbose_name=_("Project Slug")
    )
    description = models.TextField(
        blank=True, 
        null=True,
        verbose_name=_("Project Description")
    )
    start_date = models.DateField(
        verbose_name=_("Start Date"),
    )
    end_date = models.DateField(
        null=True, 
        blank=True,
        verbose_name=_("End Date")
    )
    actual_end_date = models.DateField(
        null=True, 
        blank=True,
        verbose_name=_("Actual End Date")
    )
    estimated_hours = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Estimated Hours")
    )
    status = models.CharField(
        max_length=20,
        choices=PROJECT_STATUS_CHOICES,
        default=PROJECT_STATUS_CHOICES[0][0],
        verbose_name=_("Project Status")
    )
    priority = models.CharField(
        max_length=20,
        choices=PROJECT_PRIORITY_CHOICES,
        default=PROJECT_PRIORITY_CHOICES[0][0],
        verbose_name=_("Project Priority")
    )
    type = models.CharField(
        max_length=20,
        choices=PROJECT_TYPE_CHOICES,
        default=PROJECT_TYPE_CHOICES[0][0],
        verbose_name=_("Project Type")
    )
    team = models.ForeignKey(
        "kanban.Team",
        on_delete=models.SET_NULL,
        null=True,
        related_name="projects",
        verbose_name=_("Team")
    )
    stakeholders = models.ManyToManyField(
        User,
        related_name="stakeholder_projects",
        blank=True,
        verbose_name=_("Stakeholders")
    )

    class Meta:
        db_table = "kanban_projects"
        unique_together = ['company', 'slug']
        verbose_name = _("Project")
        verbose_name_plural = _("Projects")

    def __str__(self):
        return f"{self.company}/{self.name}"


class ProjectPermission(BaseModel):
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name="permissions",
        verbose_name=_("Project")
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="project_permissions",
        verbose_name=_("User")
    )
    level = models.CharField(
        max_length=20,
        choices=PROJECT_PERMISSION_LEVEL_CHOICES,
        default=PROJECT_PERMISSION_LEVEL_CHOICES[2][0],
        verbose_name=_("Permission Level")
    )
    can_manage_tasks = models.BooleanField(
        default=True,
        verbose_name=_("Can Manage Tasks")
    )
    can_manage_members = models.BooleanField(
        default=False,
        verbose_name=_("Can Manage Members")
    )
    can_manage_sprints = models.BooleanField(
        default=False,
        verbose_name=_("Can Manage Sprints")
    )
    can_view_timesheets = models.BooleanField(
        default=True,
        verbose_name=_("Can View Timesheets")
    )
    can_edit_project = models.BooleanField(
        default=False,
        verbose_name=_("Can Edit Project")
    )


    class Meta:
        db_table = "kanban_project_permissions"
        unique_together = ['project', 'user']
        verbose_name = _("Project Permission")
        verbose_name_plural = _("Project Permissions")

    def __str__(self):
        return f"{self.project} - {self.user}"