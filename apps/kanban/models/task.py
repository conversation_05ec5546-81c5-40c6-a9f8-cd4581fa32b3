from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import (
    TASK_TYPE_CHOICES, TASK_STATUS_CHOICES, TASK_PRIORITY_CHOICES, TASK_SEVERITY_CHOICES,
    TASK_RESOLUTION_CHOICES, TASK_ESTIMATE_UNIT_CHOICES, BLUE_COLOR
)

User = get_user_model()


class TaskTag(models.Model):
    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_("Tag Name")
    )
    color_code = models.CharField(
        max_length=7,
        default=BLUE_COLOR,
        verbose_name=_("Color Code")
    )

    class Meta:
        db_table = "kanban_task_tags"
        verbose_name = _("Task Tag")
        verbose_name_plural = _("Task Tags")

    def __str__(self):
        return self.name


class TaskChecklistItem(BaseModel):
    task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="checklist_items",
        verbose_name=_("Task")
    )
    title = models.CharField(
        max_length=255,
        verbose_name=_("Title")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Checklist Item Description")
    )
    is_completed = models.BooleanField(
        default=False,
        verbose_name=_("Is Completed")
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Order")
    )
    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="completed_checklists",
        verbose_name=_("Completed By")
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Completed At")
    )

    class Meta:
        db_table = "kanban_task_checklist_items"
        verbose_name = _("Task Checklist Item")
        verbose_name_plural = _("Task Checklist Items")
        ordering = ['order']


class Task(BaseModel):
    project = models.ForeignKey(
        'kanban.Project',
        on_delete=models.CASCADE,
        related_name="tasks",
        verbose_name=_("Project")
    )
    board = models.ForeignKey(
        'kanban.Board',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="tasks",
        verbose_name=_("Board")
    )
    column = models.ForeignKey(
        'kanban.BoardColumn',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="tasks",
        verbose_name=_("Column")
    )
    parent_task = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="subtasks",
        verbose_name=_("Parent Task")
    )
    title = models.CharField(
        max_length=255,
        verbose_name=_("Task Title")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Task Description")
    )
    type = models.CharField(
        max_length=20,
        choices=TASK_TYPE_CHOICES,
        default=TASK_TYPE_CHOICES[2][0],
        verbose_name=_("Task Type")
    )
    status = models.CharField(
        max_length=20,
        choices=TASK_STATUS_CHOICES,
        default=TASK_STATUS_CHOICES[0][0],
        verbose_name=_("Task Status")
    )
    priority = models.CharField(
        max_length=20,
        choices=TASK_PRIORITY_CHOICES,
        default=TASK_PRIORITY_CHOICES[2][0],
        verbose_name=_("Task Priority")
    )
    severity = models.CharField(
        max_length=20,
        choices=TASK_SEVERITY_CHOICES,
        default=TASK_SEVERITY_CHOICES[2][0],
        verbose_name=_("Task Severity")
    )
    resolution = models.CharField(
        max_length=20,
        choices=TASK_RESOLUTION_CHOICES,
        default=TASK_RESOLUTION_CHOICES[5][0],
        blank=True,
        null=True,
        verbose_name=_("Task Resolution")
    )
    estimate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Estimated Hours")
    )
    estimate_unit = models.CharField(
        max_length=20,
        choices=TASK_ESTIMATE_UNIT_CHOICES,
        default=TASK_ESTIMATE_UNIT_CHOICES[0][0],
        verbose_name=_("Estimate Unit")
    )
    time_spent = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_("Time Spent")
    )
    time_remaining = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name=_("Remaining Time")
    )
    original_estimate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_("Original Estimate")
    )
    due_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Due Date")
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Completed At")
    )
    started_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Started At")
    )
    sprint = models.ForeignKey(
        "kanban.Sprint",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="tasks",
        verbose_name=_("Sprint")
    )
    assignees = models.ManyToManyField(
        User,
        related_name="assigned_tasks",
        blank=True,
        verbose_name=_("Assignees")
    )
    watchers = models.ManyToManyField(
        User,
        related_name="watched_tasks",
        blank=True,
        verbose_name=_("Watchers")
    )
    tags = models.ManyToManyField(
        TaskTag,
        related_name="tasks",
        blank=True,
        verbose_name=_("Tags")
    )
    rank = models.IntegerField(
        default=0,
        verbose_name=_("Rank"),
        help_text=_("Used for ordering tasks within a column.")
    )
    is_blocked = models.BooleanField(
        default=False,
        verbose_name=_("Is Blocked")
    )
    blocked_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Blocked Reason")
    )

    class Meta:
        db_table = "kanban_tasks"
        verbose_name = _("Task")
        verbose_name_plural = _("Tasks")
        ordering = ['-created_at']

    def __str__(self):
        return self.title
