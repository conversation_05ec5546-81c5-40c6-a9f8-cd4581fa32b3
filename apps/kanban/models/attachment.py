from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

User = get_user_model()


class KanbanAttachment(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="kanban_attachments",
        verbose_name=_("Company")
    )
    task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="attachments",
        verbose_name=_("Task")
    )
    file = models.FileField(
        upload_to='kanban/attachments/%Y/%m/%d/',
        verbose_name=_("File")
    )
    file_name = models.CharField(
        max_length=255,
        verbose_name=_("File Name")
    )
    file_size = models.PositiveIntegerField(
        verbose_name=_("File Size (bytes)")
    )
    file_type = models.Char<PERSON>ield(
        max_length=100,
        verbose_name=_("File Type")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description")
    )
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="task_attachments",
        verbose_name=_("Uploaded By")
    )

    class Meta:
        db_table = "kanban_attachments"
        verbose_name = _("Kanban Attachment")
        verbose_name_plural = _("Kanban Attachments")
        ordering = ['-created_at']

    def __str__(self):
        return self.file_name
