from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import ACTIVITY_TYPE_CHOICES

User = get_user_model()


class ActivityLog(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="kanban_activity_logs",
        verbose_name=_("Company")
    )
    project = models.ForeignKey(
        'kanban.Project',
        on_delete=models.CASCADE,
        related_name="activity_logs",
        verbose_name=_("Project")
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="kanban_activity_logs",
        verbose_name=_("User")
    )
    activity_type = models.CharField(
        max_length=30,
        choices=ACTIVITY_TYPE_CHOICES,
        verbose_name=_("Activity Type")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description")
    )
    
    # Generic relation to any model
    content_type = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Content Type")
    )
    object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Object ID")
    )
    # Additional data in JSON format
    metadata = models.JSONField(
        blank=True,
        null=True,
        verbose_name=_("Additional Data")
    )

    class Meta:
        db_table = "kanban_activity_logs"
        verbose_name = _("Activity Log")
        verbose_name_plural = _("Activity Logs")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user} {self.activity_type} at {self.created_at}"
