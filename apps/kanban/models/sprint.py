from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import SPRINT_STATUS_CHOICES

User = get_user_model()


class Sprint(BaseModel):
    board = models.ForeignKey(
        'kanban.Board',
        on_delete=models.CASCADE,
        related_name="sprints",
        verbose_name=_("Board")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Sprint Name")
    )
    end_date = models.DateTimeField(
        verbose_name=_("End Date")
    )
    status = models.CharField(
        max_length=20,
        choices=SPRINT_STATUS_CHOICES,
        default=SPRINT_STATUS_CHOICES[0][0],
        verbose_name=_("Sprint Status")
    )
    velocity = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_("Velocity")
    )
    capacity = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_("Capacity")
    )

    class Meta:
        db_table = "kanban_sprints"
        verbose_name = _("Sprint")
        verbose_name_plural = _("Sprints")

    def __str__(self):
        return f"{self.board}/{self.name}"