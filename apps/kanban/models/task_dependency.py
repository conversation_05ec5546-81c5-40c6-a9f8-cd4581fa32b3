from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import TASK_DEPENDENCY_TYPE_CHOICES

User = get_user_model()


class TaskDependency(BaseModel):
    source_task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="dependencies",
        verbose_name=_("Source Task")
    )
    target_task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="dependent_tasks",
        verbose_name=_("Target Task")
    )
    dependency_type = models.CharField(
        max_length=20,
        choices=TASK_DEPENDENCY_TYPE_CHOICES,
        default=TASK_DEPENDENCY_TYPE_CHOICES[0][0],
        verbose_name=_("Dependency Type")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description")
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="created_dependencies",
        verbose_name=_("Created By")
    )

    class Meta:
        db_table = "kanban_task_dependencies"
        verbose_name = _("Task Dependency")
        verbose_name_plural = _("Task Dependencies")
        unique_together = ['source_task', 'target_task']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.source_task} {self.dependency_type} {self.target_task}"
