from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import MEMBER, ROLE_CHOICES

User = get_user_model()


class CompanyMember(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        related_name="memberships",
        verbose_name=_("Company")
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="company_memberships",
        verbose_name=_("User")
    )
    role = models.Char<PERSON><PERSON>(
        max_length=20,
        choices=ROLE_CHOICES,
        default=MEMBER,
        verbose_name=_("Role")
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name=_("Is Primary")
    )

    class Meta:
        db_table = "kanban_company_members"
        unique_together = ['company', 'user']
        verbose_name = _("Company Member")
        verbose_name_plural = _("Company Members")

    def __str__(self):
        return f"{self.user} - {self.company}"
