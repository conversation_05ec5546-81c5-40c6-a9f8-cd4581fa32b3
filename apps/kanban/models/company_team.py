from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import MEMBER_ROLE_CHOICES, DEVELOPER

User = get_user_model()


class Team(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        related_name="teams",
        verbose_name=_("Company")
    )
    department = models.ForeignKey(
        "kanban.Department",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="teams",
        verbose_name=_("Department")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Team Name")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Team Description")
    )
    members = models.ManyToManyField(
        User,
        through='TeamMember',
        through_fields=('team', 'user'),
        related_name="kanban_team_memberships",
        verbose_name=_("Team Members")
    )

    class Meta:
        db_table = "kanban_teams"
        unique_together = ['company', 'name']
        verbose_name = _("Team")
        verbose_name_plural = _("Teams")

    def __str__(self):
        return f"{self.company}/{self.name}"


class TeamMember(BaseModel):
    team = models.ForeignKey(
        "kanban.Team",
        on_delete=models.CASCADE,
        related_name="team_members",
        verbose_name=_("Team")
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="kanban_team_member_roles",
        verbose_name=_("User")
    )
    role = models.CharField(
        max_length=20,
        choices=MEMBER_ROLE_CHOICES,
        default=DEVELOPER,
        verbose_name=_("Role")
    )

    class Meta:
        db_table = "kanban_team_members"
        unique_together = ['team', 'user']
        verbose_name = _("Team Member")
        verbose_name_plural = _("Team Members")

    def __str__(self):
        return f"{self.team} - {self.user}"