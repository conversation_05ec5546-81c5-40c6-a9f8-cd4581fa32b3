from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.kanban.constants import CUSTOM_FIELD_TYPE_CHOICES

User = get_user_model()


class CustomField(BaseModel):
    project = models.ForeignKey(
        'kanban.Project',
        on_delete=models.CASCADE,
        related_name="custom_fields",
        verbose_name=_("Project")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Field Name")
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description")
    )
    field_type = models.CharField(
        max_length=20,
        choices=CUSTOM_FIELD_TYPE_CHOICES,
        default=CUSTOM_FIELD_TYPE_CHOICES[0][0],
        verbose_name=_("Field Type")
    )
    is_required = models.BooleanField(
        default=False,
        verbose_name=_("Is Required")
    )
    default_value = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Default Value")
    )
    options = models.JSONField(
        blank=True,
        null=True,
        verbose_name=_("Options")
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Display Order")
    )

    class Meta:
        db_table = "kanban_custom_fields"
        verbose_name = _("Custom Field")
        verbose_name_plural = _("Custom Fields")
        unique_together = ['project', 'name']
        ordering = ['order']

    def __str__(self):
        return f"{self.project}/{self.name}"


class CustomFieldValue(BaseModel):
    task = models.ForeignKey(
        'kanban.Task',
        on_delete=models.CASCADE,
        related_name="custom_field_values",
        verbose_name=_("Task")
    )
    field = models.ForeignKey(
        CustomField,
        on_delete=models.CASCADE,
        related_name="values",
        verbose_name=_("Custom Field")
    )
    value = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Field Value")
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_custom_field_values",
        verbose_name=_("Updated By")
    )

    class Meta:
        db_table = "kanban_custom_field_values"
        verbose_name = _("Custom Field Value")
        verbose_name_plural = _("Custom Field Values")
        unique_together = ['task', 'field']

    def __str__(self):
        return f"{self.task}/{self.field.name}: {self.value}"
