from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

User = get_user_model()


class Department(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.CASCADE,
        related_name="departments",
        verbose_name=_("Company")
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_("Department Name")
    )
    description = models.TextField(
        blank=True, 
        null=True,
        verbose_name=_("Department Description")
    )
    head = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="headed_departments",
        verbose_name=_("Department Head")
    )

    class Meta:
        db_table = "kanban_departments"
        unique_together = ['company', 'name']
        verbose_name = _("Department")
        verbose_name_plural = _("Departments")

    def __str__(self):
        return f"{self.company}/{self.name}"