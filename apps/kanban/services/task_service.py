from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories import TaskRepository
from apps.kanban.services import BoardService, BoardColumnService


class TaskService(BaseService):
    def __init__(self):
        super().__init__(TaskRepository())
        self.board_service = BoardService()
        self.board_column_service = BoardColumnService()
    
    def create_task(self, data):
        try:
            board = data.get('board')
            project = data.get('project')
            if not board:
                board = self.board_service.get_or_create(project=project)
            column = data.get('coloumn')
            if not column:
                column = self.board_column_service.get_or_create(board=board)
            data['board'] = board
            data['column'] = column

            return self.create(**data)
        
        except Exception as e:
            raise ServiceError(f"Service error during create operation: {e}")

    def get_tasks_by_board(self, project_id, board_id):
        try:
            tasks = self.list(project_id=project_id, board_id=board_id).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
        
    def get_tasks_by_sprint(self, project_id, sprint_id):        
        try:
            tasks = self.list(project_id=project_id, sprint_id=sprint_id).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_by_column(self, project_id, column_id):
        try:
            tasks = self.list(project_id=project_id, column_id=column_id).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_by_assignee(self, project_id, user_id):
        try:
            tasks = self.list(project_id=project_id, assignees__id=user_id).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def search_tasks(self, project_id, search_term):
        try:
            result = self.repository.search_tasks(project_id, search_term )

            return result

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
        
    def get_blocked_tasks(self):
        try:
            tasks = self.list(is_blocked=True).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_by_status(self, project_id, status):
        try:
            tasks = self.list(project_id=project_id, status=status).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_by_priority(self, project_id, priority):
        try:
            tasks = self.list(project_id=project_id, priority=priority).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_by_type(self, project_id, task_type):
        try:
            tasks = self.list(project_id=project_id, task_type=task_type).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_tasks_due_soon(self, days=7):
        try:
            tasks = self.repository.get_tasks_due_soon(days).order_by('-created_at')

            return tasks

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def change_task_status(self, task_id, new_status):
        try:
            task = self.get(id=task_id)
            if task:
                return self.update(task, status=new_status)
            
            return None
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def assign_task(self, task_id, user_ids):
        try:
            task = self.get(id=task_id)
            if task:
                task.assignees.clear()
                for user_id in user_ids:
                    task.assignees.add(user_id)
                return task
            
            return None
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def move_task_to_column(self, task_id, column_id):
        try:
            task = self.get(id=task_id)
            if task:
                return self.update(task, column_id=column_id)
            
            return None
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
