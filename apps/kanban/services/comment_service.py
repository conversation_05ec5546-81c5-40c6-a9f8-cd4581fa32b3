from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories import CommentRepository


class CommentService(BaseService):
    def __init__(self):
        super().__init__(CommentRepository())
    
    def get_root_comments_by_task(self, task_id):
        try:
            comments = self.list(task_id=task_id).order_by('-created_at')

            return comments
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def get_replies_for_comment(self, comment_id):
        try:
            comments = self.list(parent_comment_id=comment_id).order_by('-created_at')

            return comments
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")