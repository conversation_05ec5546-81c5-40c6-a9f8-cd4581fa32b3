from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories.project_repository import (
    ProjectRepository, ProjectCategoryRepository, ProjectPermissionRepository
)


class ProjectService(BaseService):
    def __init__(self):
        super().__init__(ProjectRepository())
        self.category_repository = ProjectCategoryRepository()
        self.permission_repository = ProjectPermissionRepository()
    
    def search_projects(self, search_term):
        try:
            projects = self.repository.search_projects(search_term)

            return projects
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    

class ProjectCategoryService(BaseService):
    def __init__(self):
        super().__init__(ProjectCategoryRepository())
    
    
class ProjectPermissionService(BaseService):
    def __init__(self):
        super().__init__(ProjectPermissionRepository())
    
    def set_permission(self, project_id, user_id, level, **permissions):
        try:
            permission = self.get(project_id=project_id, user_id=user_id)
            
            if permission:
                # Update existing permission
                update_data = {'level': level, **permissions}
                return self.update(permission, **update_data)
            
            permission_data = {
                'project_id': project_id,
                'user_id': user_id,
                'level': level,
                **permissions
            }

            return self.create(**permission_data)
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
    
    def remove_permission(self, project_id, user_id):
        try:
            permission = self.get(project_id=project_id, user_id=user_id)
            if permission:
                self.delete(permission)
                return True
            
            return False
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")