from django.db import models, transaction
from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories import BoardColumnRepository


class BoardColumnService(BaseService):
    def __init__(self):
        super().__init__(BoardColumnRepository())

    def get_or_create(self, board):
        try:
            column = self.get(board=board, name="To Do")
            if not column:
                column = self.create(board=board, name="To Do")

            return column
        
        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")
        
    def get_max_order(self, board):
        try:
            max_order = self.list(board=board).aggregate(models.Max("order"))["order__max"]

            return max_order or 0

        except Exception as e:
            raise ServiceError(f"Service error during get operation: {e}")

    def reorder_columns(self, board_id, ordered_column_ids):
        try:
            with transaction.atomic():
                columns = list(
                    self.list(board_id=board_id, id__in=ordered_column_ids).select_for_update()
                )

                if len(columns) != len(ordered_column_ids):
                    raise ValueError("Some column IDs are invalid or not part of the specified board.")

                column_map = {column.id: column for column in columns}

                temp_order_start = -1000
                for i, column_id in enumerate(ordered_column_ids):
                    column = column_map[column_id]
                    column.order = temp_order_start - i

                self.bulk_update(list(column_map.values()), ['order'])

                reordered_columns = [column_map[col_id] for col_id in ordered_column_ids]
                for index, column in enumerate(reordered_columns, start=1):
                    column.order = index

                self.bulk_update(reordered_columns, ['order'])

        except Exception as e:
            raise ServiceError(f"Service error during reorder operation: {e}")
