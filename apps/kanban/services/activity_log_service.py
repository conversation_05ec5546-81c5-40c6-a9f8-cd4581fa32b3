from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories import ActivityLogRepository


class ActivityLogService(BaseService):
    def __init__(self):
        super().__init__(ActivityLogRepository())
    
    def log_activity(self, project_id, user_id, activity_type, description=None, content_type=None, object_id=None, data=None):
        try:
            activity_data = {
                'project_id': project_id,
                'user_id': user_id,
                'activity_type': activity_type,
                'description': description,
                'content_type': content_type,
                'object_id': object_id,
                'data': data
            }
            log = self.create(**activity_data)

            return log
        
        except Exception as e:
            raise ServiceError(f"Service error during create operation: {e}")
