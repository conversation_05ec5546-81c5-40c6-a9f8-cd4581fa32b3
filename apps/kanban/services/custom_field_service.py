from dxh_common.base.base_service import BaseService, ServiceError
from apps.kanban.repositories import CustomFieldRepository, CustomFieldValueRepository


class CustomFieldService(BaseService):
    def __init__(self):
        super().__init__(CustomFieldRepository())
        self.value_repository = CustomFieldValueRepository()


class CustomFieldValueService(BaseService):
    def __init__(self):
        super().__init__(CustomFieldValueRepository())
        self.field_repository = CustomFieldRepository()
    
    def set_field_value(self, task_id, field_id, value, user):
        try: 
            existing_value = self.get(task_id=task_id, field_id=field_id)
            
            if existing_value:
                field = self.update(
                    existing_value, 
                    value=value,
                    updated_by=user
                )
            else:
                value_data = {
                    'task_id': task_id,
                    'field_id': field_id,
                    'value': value,
                    'updated_by': user,
                    'created_by': user
                }
                field = self.create(**value_data)
            
            return field
        
        except Exception as e:
            raise ServiceError(f"Service error during set_field_value operation: {e}")
    