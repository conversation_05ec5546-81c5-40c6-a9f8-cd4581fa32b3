from urllib.parse import urlencode
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework_simplejwt import RefreshToken
from dxh_common.logger import Logger
from dxh_common.base.base_service import BaseService
from dxh_common.base.base_service import ServiceError
from dxh_common.helpers.validators import validate_password_strength
from dxh_common.utils.enums import VerificationCodeType, VerificationTokenType
from dxh_common.services.celery_service import schedul_task, cancel_task_by_name

from apps.core.services import SystemSettingService
from apps.cross_cutting.email_service import EmailService
from apps.identity.repositories import UserRepository
from apps.identity.services.verification_code_service import VerificationCodeService
from apps.identity.services.verification_token_service import VerificationTokenService
from apps.identity.services.device_service import UserDeviceService
from apps.identity.services.session_service import UserSessionService

logger = Logger(__name__)


class UserService(BaseService):
    def __init__(self):
        super().__init__(UserRepository())
        self.verification_code_service = VerificationCodeService()
        self.verification_token_service = VerificationTokenService()
        self.email_service = EmailService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()
        self.system_setting_service = SystemSettingService()

    def verify_email(self, email, code, type):
        try:
            verification_code = self.verification_code_service.get(
                user__email=email,
                code=code,
                type=type,
                is_used=False,
            )
            if not verification_code or verification_code.expires_at < timezone.now():
                return False, None

            verification_code.is_used = True
            verification_code.save()
            user = verification_code.user

            if type == VerificationCodeType.EMAIL_OTP.value:
                user.is_email_verified = True
                user.is_active = True
                user.save()

            logger.info(f"Email verification successful for user {user.email}")

            if type == VerificationCodeType.PASSWORD_RESET_OTP.value:
                verification_token = self.verification_token_service.create_verification_token(
                    user, token_type=VerificationTokenType.PASSWORD_RESET.value)
                return True, verification_token

            return True, None

        except Exception as e:
            logger.error(f"Error verifying email with code {code}: {str(e)}")
            raise ServiceError(f"Error verifying email: {str(e)}")

    def verify_phone(self, code):
        try:
            verification = self.verification_code_service.get(
                code=code,
                type=VerificationCodeType.PASSWORD_RESET_OTP.value,
                is_used=False,
                expires_at__gte=timezone.now(),
            )

            if verification:
                verification.user.is_phone_verified = True
                verification.user.save()
                verification.is_used = True
                verification.save()
                logger.info(
                    f"Phone verification successful for user {verification.user.email}")
                return True
            
            return False

        except Exception as e:
            logger.error(f"Error verifying phone with code {code}: {str(e)}")
            raise ServiceError(f"Error verifying phone: {str(e)}")

    def send_verification_email(self, user, code):
        try:
            system_setting = self.system_setting_service.get(company=user.company)
            if not system_setting:
                raise ServiceError("System setting not configured")
            
            expiry = system_setting.otp_expire_time

            self.email_service.send_email(
                to_email=user.email,
                subject="Verify your email",
                template_name="email_verification_otp",
                context={
                    "user": {
                    "id": user.pk,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "username": user.username,
                    }, 
                    "otp": code, 
                    "expiry": expiry},
            )
            logger.info(f"Verification email sent to {user.email}")

        except Exception as e:
            logger.error(
                f"Error sending verification email to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending verification email: {str(e)}")

    def resend_email(self, user, code):
        try:
            system_setting = self.system_setting_service.get(company=user.company)
            if not system_setting:
                raise ServiceError("System setting not configured")
            
            expiry = system_setting.otp_expire_time

            self.email_service.send_email(
                to_email=user.email,
                subject="Resend your email",
                template_name="email_verification_otp",
                context={
                    "user": {
                        "id": user.pk,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "username": user.username,
                    }, 
                    "otp": code, 
                    "expiry": expiry},
            )
            logger.info(f"Resend email sent to {user.email}")

        except Exception as e:
            logger.error(
                f"Error sending resend email to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending resend email: {str(e)}")

    def refresh_token(self, refresh_token):
        try:
            refresh = RefreshToken(refresh_token)
            tokens = {
                "access": str(refresh.access_token),
                "refresh": str(refresh),
            }
            logger.info("Token refreshed successfully")
            
            return tokens
        
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise ServiceError(f"Error refreshing token: {str(e)}")

    def _send_reset_password_email(self, user, code, verification_token):
        try:
            system_setting = self.system_setting_service.get(company=user.company)
            if not system_setting:
                raise ServiceError("System setting not configured")

            expiry = system_setting.otp_expire_time

            params = {
                "code": code,
                "token": verification_token.token,
                "email": user.email
            }
            reset_link = f"{settings.FRONTEND_URL}/auth/new-password?{urlencode(params)}"
            self.email_service.send_email(
                to_email=user.email,
                subject="Reset your password",
                template_name="password_reset_link",
                context={
                    "user": {
                        "id": user.pk,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "username": user.username,
                    },
                    "reset_link": reset_link,
                    "otp": code,
                    "token": verification_token.token,
                    "expiry": expiry,
                },
            )
            logger.info(f"Reset password link sent to {user.email}")
        except Exception as e:
            logger.error(
                f"Error sending reset password link to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending reset password link: {str(e)}")

    def forgot_password(self, email):
        try:
            user = self.repository.get(email=email)
            if not user:
                return False
            
            type = VerificationCodeType.PASSWORD_RESET_OTP.value
            code = self.verification_code_service.create_verification_code(
                user, code_type=type)

            verification_token = self.verification_token_service.create_verification_token(
                user, 
                token_type=VerificationTokenType.PASSWORD_RESET.value
            )

            self._send_reset_password_email(user=user, code=code, verification_token=verification_token)

            logger.info(f"Forgot password email sent to {user.email}")

            return True

        except Exception as e:
            logger.error(
                f"Error sending forgot password email to {email}: {str(e)}")
            raise ServiceError(
                f"Error sending forgot password email: {str(e)}")

    def reset_password(self, token, new_password):
        try:
            try:
                validate_password_strength(new_password)
            except ValidationError as e:
                return False, str(list(e)[0])

            verification_token = self.verification_token_service.verify_token(
                token=token,
                token_type=VerificationTokenType.PASSWORD_RESET.value
            )

            if not verification_token:
                return False, _("Invalid or expired token")

            # Get user from token
            user = verification_token.user
            if not user:
                return False, _("User not found")

            # Set new password
            user.set_password(new_password)
            user.save()

            # Mark token as used
            self.verification_token_service.mark_used(verification_token)

            logger.info(f"Password reset successful for user {user.email}")
            return True, _("Password reset successful")

        except Exception as e:
            logger.error(f"Error resetting password: {str(e)}")
            raise ServiceError(f"Error resetting password: {str(e)}")

    def change_password(self, user, old_password, new_password):
        try:
            try:
                validate_password_strength(new_password)
            except ValidationError as e:
                raise ServiceError(f"Invalid password: {str(list(e)[0])}")

            if not user.check_password(old_password):
                return False, _("Old password is incorrect")

            user.set_password(new_password)
            user.save()
            logger.info(f"Password changed successfully for user {user.email}")

            return True, _("Password changed successfully")
        
        except Exception as e:
            logger.error(f"Error changing password {user.email}: {str(e)}")
            raise ServiceError(f"Error resetting password: {str(e)}")
    
    def send_verification_link(self, user, code):
        try:
            system_setting = self.system_setting_service.get(company=user.company)
            if not system_setting:
                raise ServiceError("System setting not configured")
            
            expiry = system_setting.otp_expire_time

            params = {
                "code": code,
                "email": user.email
            }
            verification_link = f"{settings.FRONTEND_URL}/verify-email?{urlencode(params)}"

            self.email_service.send_email(
                to_email=user.email,
                subject="Verify your email",
                template_name="email_verification_link",
                context={
                    "user": {
                        "id": user.pk,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "username": user.username,
                    },
                    "verification_link": verification_link,
                    "otp": code,
                    "expiry": expiry,
                },
            )
            logger.info(f"Verification link sent to {user.email}")

        except Exception as e:
            logger.error(f"Error sending verification link to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending verification link: {str(e)}")

    def create_user(self, data):
        try:
            user = self.repository.create(**data)
            user.set_password(data['password'])
            user.save()

            verification_code = self.verification_code_service.create_verification_code(
                user, VerificationCodeType.EMAIL_OTP.value
            )

            try:
                self.send_verification_link(
                    user=user, 
                    code=verification_code
                )
            except Exception as email_error:
                logger.warning(
                    f"Failed to send verification email to {user.email}: {str(email_error)}")

            return user
        
        except Exception as e:
            logger.error(
                f"Error creating user with email {data['email']}: {str(e)}")
            raise ServiceError(f"Error creating user: {str(e)}")
    
    def is_user_registered(self, email, username):
        try:

            user = self.repository.get(email=email)
            if user and user.is_active:
                return True, _("User with this email already exists")
            
            user = self.repository.get(username=username)
            if user and user.is_active:
                return True, _("User with this username already exists")
            
            return False, None
        
        except Exception as e:
            logger.error(f"Error checking if user is registered: {str(e)}")
            raise ServiceError(f"Error checking if user is registered: {str(e)}")
    
    def is_registration_completed(self, email, username):
        try:
            user = self.repository.get(email=email) or self.repository.get(
                username=username
            )
            if user and not user.is_active and not user.is_email_verified:
                verification_code = (
                    self.verification_code_service.create_verification_code(
                        user, VerificationCodeType.EMAIL_OTP.value
                    )
                )
                try:
                    self.send_verification_link(user=user, code=verification_code)
                except Exception as email_error:
                    logger.warning(f"Failed to send verification email to {user.email}: {str(email_error)}")

                return False, user,  _("Registration is not completed. Please check your email to verify your account.")

            return True, None, None
        
        except Exception as e:
            logger.error(f"Error checking if registration is complete: {str(e)}")
            raise ServiceError(f"Error checking if registration is complete: {str(e)}")
    
    def set_user_permissions(self, user, permissions):
        try:
            user.user_permissions.set(permissions)
        except Exception as e:
            logger.error(
                f"Error setting user permissions for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error setting user permissions: {str(e)}")

    def request_account_deletion(self, user, days_until_deletion):
        try:
            scheduled_time=timezone.now() + timezone.timedelta(days=days_until_deletion)
            scheduled = schedul_task(
                task = "apps.user.tasks.delete_user_after_delay",
                task_name = f"user_deletion_{user.id}", 
                scheduled_time = scheduled_time, 
            )
            if not scheduled:
                return None, scheduled

            return scheduled_time, scheduled

        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.id}: {str(e)}")
            raise ServiceError(f"Failed to request account deletion: {str(e)}")

    def cancel_deletion_request(self, task_name, reason='manual_cancellation'):
        try:
            cancelled = cancel_task_by_name(task_name)
            if cancelled:
                logger.info(f"Cancelled deletion request for {task_name}, reason: {reason}")
                return True
            
            return False

        except Exception as e:
            logger.error(f"Error cancelling deletion request for {task_name}: {str(e)}")
            return False