import secrets
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_service import BaseService, ServiceError
from apps.core.services import SystemSettingService
from apps.identity.repositories import VerificationTokenRepository
from dxh_common.utils.enums import VerificationTokenType

logger = Logger(__name__)


class VerificationTokenService(BaseService):
    def __init__(self):
        super().__init__(VerificationTokenRepository())
        self.system_setting_service = SystemSettingService()

    def create_verification_token(self, user, token_type):
        try:
            system_setting = self.system_setting_service.get(company=user.company)
            if not system_setting:
                raise ServiceError("System setting not configured")
            
            token = secrets.token_urlsafe(32)
            
            expiry = system_setting.otp_expire_time
            expires_at = timezone.now() + timezone.timedelta(minutes=expiry)
            
            verification_token = self.create(
                user=user,
                token=token,
                token_type=token_type,
                expires_at=expires_at,
            )
            
            logger.info(f"Verification token created for {user.email}")
            
            return verification_token
            
        except Exception as e:
            logger.error(f"Error creating verification token for {user.email}: {str(e)}")
            raise ServiceError(f"Error creating verification token: {str(e)}")
    
    def verify_token(self, token, token_type):
        """
        Verify if the token is valid
        """
        try:
            verification_token = self.get(
                token=token,
                token_type=token_type,
                is_used=False,
                expires_at__gte=timezone.now()
            )
            
            if not verification_token:
                return None
                
            if verification_token.attempts >= 3:
                return None
                
            return verification_token
            
        except Exception as e:
            logger.error(f"Error verifying token: {str(e)}")
            return None
    
    def increment_attempts(self, verification_token):
        """
        Increment the number of attempts for the token
        """
        try:
            verification_token.attempts += 1
            verification_token.save(update_fields=["attempts"])
            
            return verification_token
            
        except Exception as e:
            logger.error(f"Error incrementing attempts for token: {str(e)}")
            raise ServiceError(f"Error incrementing attempts: {str(e)}")
    
    def mark_used(self, verification_token):
        """
        Mark the token as used
        """
        try:
            verification_token.is_used = True
            verification_token.save(update_fields=["is_used"])
            
            return verification_token
            
        except Exception as e:
            logger.error(f"Error marking token as used: {str(e)}")
            raise ServiceError(f"Error marking token as used: {str(e)}")
