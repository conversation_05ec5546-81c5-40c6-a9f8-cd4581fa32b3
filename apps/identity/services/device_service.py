from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError
from dxh_common.base.base_service import BaseService
from apps.identity.repositories import UserDeviceRepository
from apps.identity.utils import generate_device_id
from django.contrib.gis.geoip2 import GeoIP2

logger = Logger(__name__)


class UserDeviceService(BaseService):
    def __init__(self):
        super().__init__(UserDeviceRepository())
        self.geo_ip = GeoIP2()
    
    def get_os_distribution(self):
        try:
            os_counts = self.repository.get_os_count()

            return os_counts
        
        except Exception as e:
            logger.error(f"Error getting os count: {str(e)}")
            raise ServiceError(f"Error getting os count: {str(e)}")
        
    
    def create_user_device(self, user, remote_ip, user_agent, browser_user_agent, referer, screen_resolution, language):
        try:
            browser = user_agent.browser.family
            device=user_agent.device.family
            os_version = user_agent.os.version_string
            is_mobile = user_agent.is_mobile
            is_tablet = user_agent.is_tablet
            is_pc = user_agent.is_pc
            is_touch_capable = user_agent.is_touch_capable
            is_bot = user_agent.is_bot
            os = user_agent.os.family
            user_id = user.id if user else None

            device_id = generate_device_id(device, remote_ip, os, user_id)
            existing_device = self.repository.filter(
                ip_address=remote_ip
            )
            
            if existing_device.filter(device_id=device_id) or (not user and existing_device):
                logger.info(f"Device already created for this id: {device_id}")
                return
            
            try:
                city_info = self.geo_ip.city(remote_ip)
                location = city_info['city'] if 'city' in city_info else ''

                if existing_device:
                    existing_device=existing_device.first()
                    user_device = self.get_existing_device(
                        user_device=existing_device,
                        os=os,
                        country=city_info['country_name'],
                        device=device
                    )

                    if user_device and user:
                        user_device = self.repository.update(
                            user_device,
                            device_id=device_id,
                            is_authenticated=user.is_authenticated,
                            user=user,
                            created_by=user,
                            updated_by=user,
                        )

                user_device = self.repository.create(
                    company=user.company if user and user.company else None,
                    device_id=device_id,
                    device=device,
                    user=user,
                    created_by=user,
                    updated_by=user,
                    ip_address=remote_ip,
                    browser=browser,
                    user_agent=browser_user_agent,
                    location=location,
                    referer=referer,
                    screen_resolution=screen_resolution,
                    latitude=city_info['latitude'],
                    longitude=city_info['longitude'],
                    accuracy_radius=city_info['accuracy_radius'],
                    time_zone=city_info['time_zone'],
                    continent_name=city_info['continent_name'],
                    continent_code=city_info['continent_code'],
                    country=city_info['country_name'],
                    country_code=city_info['country_code'],
                    city=city_info['city'],
                    region=city_info['region_name'],
                    region_code=city_info['region_code'],
                    postal_code=city_info['postal_code'],
                    dma_code=city_info['dma_code'],
                    is_in_european_union=city_info['is_in_european_union'],
                    language=language,
                    is_authenticated=user.is_authenticated if user else False,
                    device_type=user_agent.device.family,
                    os=user_agent.os.family,
                    os_version=os_version,
                    is_mobile=is_mobile,
                    is_tablet=is_tablet,
                    is_pc=is_pc,
                    is_touch_capable=is_touch_capable,
                    is_bot=is_bot
                )
                logger.info(f"Device created: {user_device}")

                return
            
            except Exception as e:
                logger.error(f"GeoIP2 error: {e}")
                return
        
        except Exception as e:
            logger.error(f"Can't create device: {str(e)}")

            return
        
    def get_existing_device(self, user_device, os, country, device):
        if user_device.device == device and user_device.os == os and user_device.country == country and not user_device.is_authenticated:
            return user_device
        return None