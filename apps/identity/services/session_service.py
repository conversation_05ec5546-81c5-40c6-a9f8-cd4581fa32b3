from django.utils import timezone
from datetime import timedelta
from dxh_common.logger import Logger
from dxh_common.base.base_service import BaseService, ServiceError
from apps.identity.repositories import UserSessionRepository
from apps.core.services import SystemSettingService
from apps.identity.services.device_service import UserDeviceService
from apps.identity.utils import generate_device_id

logger = Logger(__name__)


class UserSessionService(BaseService):
    def __init__(self):
        super().__init__(UserSessionRepository())
        self.system_setting_service = SystemSettingService()
        self.user_device_service = UserDeviceService()

    def create_user_session(self, user, user_agent, user_device, ip_address, tokens: dict):
        try:
            browser = user_agent.browser.family
            browser_version = user_agent.browser.version_string
            system_setting = self.system_setting_service.get(company=user.company)
            session_timeout = system_setting.session_timeout if system_setting else 30
            access_token = tokens.get("access")
            refresh_token=tokens.get("refresh")

            user_session = self.repository.create(
                user=user,
                company=user.company,
                device=user_device,
                browser=browser,
                browser_version=browser_version,
                ip_address=ip_address,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=timezone.now() + timedelta(minutes=session_timeout),
                created_by=user,

            )

            return user_session
        
        except Exception as e:
            logger.error(f"Error creating user session: {str(e)}")
            raise ServiceError(f"Error creating user session: {str(e)}")
    
    def logout_user_session(self, user_session_data):
        try:
            user = user_session_data.get("user")
            user_agent = user_session_data.get("user_agent")
            browser = user_agent.browser.family
            browser_version = user_agent.browser.version_string
            os = user_agent.os.family
            device_id = generate_device_id(browser, os, user.id)

            user_device = self.user_device_service.get(device_id=device_id, user=user)

            user_session = self.get_user_session(
                user, 
                user_device, 
                browser, 
                browser_version
            )
            
            if user_session:
                self.repository.update(
                    user_session,
                    updated_by=user,
                    logout_time=timezone.now(),
                    # deleted_at=timezone.now(),
                    is_active=False, 
                    # is_deleted=True
                )
                return user_session
            
            return None
        
        except Exception as e:
            logger.error(f"Error logging out user session: {str(e)}")
            raise ServiceError(f"Error logging out user session: {str(e)}")

    def get_user_session(self, user, user_device, browser, browser_version):
        try:
            user_sessions = self.repository.filter(
                user=user,
                device=user_device,
                browser=browser,
                browser_version=browser_version,
                logout_time__isnull=True
            )

            if user_sessions:
                user_session = user_sessions.order_by('-login_time').first()
                return user_session
            
            return None
        
        except Exception as e:
            logger.error(f"Error getting user session: {str(e)}")
            raise ServiceError(f"Error getting user session: {str(e)}")
        
    def get_browser_distribution(self):
        try:
            browser_counts = self.repository.get_browser_count()
            
            return browser_counts
        
        except Exception as e:
            logger.error(f"Error getting os count: {str(e)}")
            raise ServiceError(f"Error getting os count: {str(e)}")
        
