from django.db import DatabaseError
from django.db.models import Count
from dxh_common.base.base_repository import BaseRepository, RepositoryError
from apps.identity.models import UserSession


class UserSessionRepository(BaseRepository):
    def __init__(self):
        super().__init__(UserSession)

    def get_browser_count(self):
        try:
            browser_count = (
                self.model.objects.values("browser")
                .annotate(count=Count("browser"))
                .order_by("-count")
            )
            
            return browser_count

        except DatabaseError as e:
            raise RepositoryError(f"Error retrieving data: {e}")