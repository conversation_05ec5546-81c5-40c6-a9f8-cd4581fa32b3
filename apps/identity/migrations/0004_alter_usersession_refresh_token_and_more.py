# Generated by Django 5.1.5 on 2025-03-11 08:14

import dxh_libraries.encrypted_model_fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0003_navigationpermission'),
    ]

    operations = [
        migrations.AlterField(
            model_name='usersession',
            name='refresh_token',
            field=dxh_libraries.encrypted_model_fields.EncryptedCharField(unique=True, verbose_name='Refresh Token'),
        ),
        migrations.AlterField(
            model_name='usersession',
            name='token',
            field=dxh_libraries.encrypted_model_fields.EncryptedCharField(unique=True, verbose_name='Token'),
        ),
    ]
