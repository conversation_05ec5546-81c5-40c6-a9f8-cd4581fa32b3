# Generated by Django 5.1.5 on 2025-04-09 09:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0006_alter_usersession_access_token'),
    ]

    operations = [
        migrations.AddField(
            model_name='userdevice',
            name='browser',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Browser'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='device_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Device Type'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='is_authenticated',
            field=models.BooleanField(default=False, verbose_name='Is Authenticated'),
        ),
        migrations.Add<PERSON>ield(
            model_name='userdevice',
            name='language',
            field=models.Char<PERSON>ield(blank=True, max_length=10, null=True, verbose_name='Language'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='Latitude'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Location'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='Longitude'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='referrer',
            field=models.URLField(blank=True, null=True, verbose_name='Referrer'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='screen_resolution',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Screen Resolution'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='session_key',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='Session Key'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='user_agent',
            field=models.TextField(blank=True, null=True, verbose_name='User Agent'),
        ),
    ]
