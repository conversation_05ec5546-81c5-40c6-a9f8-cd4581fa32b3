# Generated by Django 5.1.5 on 2025-05-18 13:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0010_remove_userdevice_session_key_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userdevice',
            name='continent_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Continent Code'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='country_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Country Code'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='dma_code',
            field=models.Char<PERSON>ield(blank=True, help_text='Designated Market Area code, used in the United States to identify television markets.', max_length=10, null=True, verbose_name='DMA Code'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='metro_code',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=20, null=True, verbose_name='Metro Code'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='region',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Region'),
        ),
        migrations.AlterField(
            model_name='userdevice',
            name='region_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Region Code'),
        ),
    ]
