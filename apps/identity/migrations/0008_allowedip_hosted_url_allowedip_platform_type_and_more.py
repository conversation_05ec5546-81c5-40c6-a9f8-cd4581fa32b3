# Generated by Django 5.1.5 on 2025-04-25 19:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0007_userdevice_browser_userdevice_device_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='allowedip',
            name='hosted_url',
            field=models.URLField(blank=True, help_text='URL of the hosted service from which the IP address is allowed', max_length=500, null=True, verbose_name='Hosted URL'),
        ),
        migrations.AddField(
            model_name='allowedip',
            name='platform_type',
            field=models.CharField(choices=[('web', 'Web'), ('mobile', 'Mobile'), ('api', 'API'), ('admin', 'Admin')], help_text='Type of platform from which the IP address is allowed', max_length=50, null=True, verbose_name='Platform Type'),
        ),
        migrations.AddField(
            model_name='blockedip',
            name='hosted_url',
            field=models.URLField(blank=True, help_text='URL of the hosted service from which the IP address is blocked', max_length=500, null=True, verbose_name='Hosted URL'),
        ),
        migrations.AddField(
            model_name='blockedip',
            name='platform_type',
            field=models.CharField(choices=[('web', 'Web'), ('mobile', 'Mobile'), ('api', 'API'), ('admin', 'Admin')], help_text='Type of platform from which the IP address is blocked', max_length=50, null=True, verbose_name='Platform Type'),
        ),
        migrations.AlterField(
            model_name='allowedip',
            name='reason',
            field=models.CharField(blank=True, help_text='Reason for allowing the IP address', max_length=255, null=True, verbose_name='Reason'),
        ),
        migrations.AlterField(
            model_name='blockedip',
            name='ip_address',
            field=models.GenericIPAddressField(unique=True, verbose_name='IP Address'),
        ),
        migrations.AlterField(
            model_name='blockedip',
            name='reason',
            field=models.CharField(blank=True, help_text='Reason for blocking the IP address', max_length=255, null=True, verbose_name='Reason'),
        ),
    ]
