# Generated by Django 5.1.5 on 2025-03-05 06:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_navigation'),
        ('identity', '0002_group_company'),
    ]

    operations = [
        migrations.CreateModel(
            name='NavigationPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at')),
                ('deleted_at', models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='Is deleted')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('is_view', models.BooleanField(default=False, verbose_name='View')),
                ('is_edit', models.BooleanField(default=False, verbose_name='Edit')),
                ('is_delete', models.BooleanField(default=False, verbose_name='Delete')),
                ('is_create', models.BooleanField(default=False, verbose_name='Create')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_navigation_permissions', to='core.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by')),
                ('group', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='identity.group', verbose_name='Group')),
                ('navigation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.navigation', verbose_name='Navigation')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by')),
            ],
            options={
                'verbose_name': 'Navigation Permission',
                'verbose_name_plural': 'Navigation Permissions',
                'db_table': 'app_navigation_permissions',
                'unique_together': {('navigation', 'group')},
            },
        ),
    ]
