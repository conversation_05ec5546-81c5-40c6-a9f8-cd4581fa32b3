# Generated by Django 5.1.5 on 2025-05-18 13:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0009_alter_verificationtoken_token'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userdevice',
            name='session_key',
        ),
        migrations.AddField(
            model_name='userdevice',
            name='accuracy_radius',
            field=models.IntegerField(blank=True, help_text='Radius in kilometers around the latitude and longitude where the device is likely located.', null=True, verbose_name='Accuracy Radius'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='City'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='continent_code',
            field=models.CharField(blank=True, max_length=2, null=True, verbose_name='Continent Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='continent_name',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Continent Name'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Country Name'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='country_code',
            field=models.CharField(blank=True, max_length=2, null=True, verbose_name='Country Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='dma_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='DMA Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='is_in_european_union',
            field=models.BooleanField(default=False, verbose_name='Is in European Union'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='metro_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Metro Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Postal Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='region',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Region'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='region_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Region Code'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='region_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Region Name'),
        ),
        migrations.AddField(
            model_name='userdevice',
            name='time_zone',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Time Zone'),
        ),
    ]
