# Generated by Django 5.1.5 on 2025-03-11 08:21

import dxh_libraries.encrypted_model_fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('identity', '0004_alter_usersession_refresh_token_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='usersession',
            name='token',
        ),
        migrations.AddField(
            model_name='usersession',
            name='access_token',
            field=dxh_libraries.encrypted_model_fields.EncryptedCharField(default='access_token', verbose_name='Access Token'),
            preserve_default=False,
        ),
    ]
