from django.utils.deprecation import MiddlewareMixin
from dxh_common.logger import Logger
from apps.identity.services import UserDeviceService

logger = Logger(__name__)


class XForwardedForMiddleware(MiddlewareMixin):
    """
    Middleware to handle X-Forwarded-For header for proper IP detection
    when behind a proxy (e.g., Docker, load balancers).
    """
    def process_request(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0].strip()
            request.META['REMOTE_ADDR'] = client_ip
        

class DeviceTrackMiddleware(MiddlewareMixin):
    def __init__(self, get_response):
        self.device_service = UserDeviceService()
        super().__init__(get_response)

    def process_response(self, request, response):
        try:
            user = request.user if request.user.is_authenticated else None
            remote_ip = request.META.get('REMOTE_ADDR', '')
            u_agent = request.META.get('HTTP_USER_AGENT', '')
            referer = request.META.get('HTTP_REFERER', '')
            screen_resolution = request.META.get('HTTP_SCREEN_RESOLUTION', '')
            language = request.META.get('HTTP_ACCEPT_LANGUAGE', '').split(',')[0]
            
            user_agent = request.user_agent
            device = self.device_service.create_user_device(
                user,
                remote_ip, 
                user_agent, 
                u_agent,
                referer, 
                screen_resolution,
                language
            )

        except Exception as e:
            logger.error(f"Error in RequestEventEnrichMiddleware: {e}")

        return response
