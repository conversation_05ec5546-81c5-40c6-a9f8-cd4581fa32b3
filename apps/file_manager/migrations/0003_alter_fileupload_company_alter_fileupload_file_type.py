# Generated by Django 5.1.5 on 2025-05-19 08:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_filetype_company_language_company'),
        ('file_manager', '0002_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fileupload',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_file_uploads', to='core.company', verbose_name='Company'),
        ),
        migrations.AlterField(
            model_name='fileupload',
            name='file_type',
            field=models.CharField(db_index=True, max_length=50, verbose_name='File Type'),
        ),
    ]
