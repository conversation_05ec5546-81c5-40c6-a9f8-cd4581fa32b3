# Generated by Django 5.1.5 on 2025-05-19 10:32

import apps.file_manager.models.file_upload
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('file_manager', '0003_alter_fileupload_company_alter_fileupload_file_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fileupload',
            name='file',
            field=models.FileField(upload_to=apps.file_manager.models.file_upload.dynamic_upload_path, verbose_name='File'),
        ),
    ]
