from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.drf_yasg import swagger_auto_schema
from dxh_libraries.rest_framework import Response, status
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.paginations import CustomPagination
from dxh_common.logger import Logger

from apps.product.services import CategoryService
from apps.product.api.v1.serializers import CategorySerializer, UpdateCategorySerializer, DeleteCategorySerializer

logger = Logger(__name__)


class CategoryListView(BaseApiView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.category_service = CategoryService()
        self.paginator = CustomPagination()
        self.category_serializer = CategorySerializer

    def get(self, request):
        try:
            name = request.query_params.get('name')
            categories = self.category_service.get_all_categories(name=name)
            paginated_data = self.paginator.paginate_queryset(categories, request)
            serializer = self.category_serializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Categories retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CategoryListView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=CategorySerializer)
    def post(self, request):
        try:
            serializer = self.category_serializer(data=request.data)

            if not serializer.is_valid():
                result = {
                    "message": _("Failed to create category"),
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            data = serializer.validated_data
            data['company'] = request.user.company
            data['created_by'] = request.user

            category = self.category_service.create(**data)
            result = {
                "message": _("Category created successfully"),
                "data": self.category_serializer(category).data,
            }
            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "CategoryListView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=UpdateCategorySerializer)
    def put(self, request):
        try:
            category_id = request.data.get("id")
            if not category_id:
                result = {
                    "message": _("Category ID is required"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            category = self.category_service.get_category(category_id)
            if not category:
                result = {
                    "message": _("Category not found"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            serializer = self.category_serializer(instance=category, data=request.data, partial=True)

            if not serializer.is_valid():
                result = {
                    "message": _("Failed to update category"),
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            data = serializer.validated_data
            data['updated_by'] = request.user
            updated_category = self.category_service.update(instance=category, **data)
            result = {
                "message": _("Category updated successfully"),
                "data": self.category_serializer(updated_category).data,
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CategoryListView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    @swagger_auto_schema(request_body=DeleteCategorySerializer)
    def delete(self, request):
        try:
            category_id = request.data.get("id")
            if not category_id:
                result = {
                    "message": _("Category ID is required"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            category = self.category_service.get(id=category_id)
            if not category:
                result = {
                    "message": _("Category not found"),
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

            self.category_service.delete(category)
            result = {
                "message": _("Category deleted successfully"),
            }
            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "CategoryListView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e
