from apps.user.configs import UserExportResource
from django.core.exceptions import ImproperlyConfigured

class UserExportService:
    def __init__(self):
        self.resource = UserExportResource()

    def export_data(self, format_type, user_ids=None):
        try:
            dataset = self.resource.export(user_ids=user_ids)
            if format_type == 'json':
                return dataset.json
            
            elif format_type == 'html':
                return dataset.html
            
            elif format_type == 'tsv':
                return dataset.tsv

            elif format_type == 'yaml':
                return dataset.yaml
            
            return dataset.csv
        
        except Exception as e:
            raise ImproperlyConfigured(f"Error exporting data: {str(e)}")