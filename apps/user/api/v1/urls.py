from django.urls import path
from apps.user.api.v1 import views

urlpatterns = [
    # Users
    path('users/me/', views.MeView.as_view(), name='user-me'),
    path('users/', views.UserAPIView.as_view(), name='user-list'),
    path('users/<int:user_id>/', views.UserAPIView.as_view(), name='user-update-delete'),
    path('users/search/', views.UserSearchView.as_view(), name='user-search'),
    path('users/update-profile/', views.UserProfileView.as_view(), name='update-profile'),
    path('users/counts/', views.UserCountView.as_view(), name='user-count'),
    path('users/export/', views.UserExportView.as_view(), name='user-export'),
    # path('users/<int:user_id>/status/', views.UserStatusView.as_view(), name='user-status'),
    # path('users/filters/', views.UserFilterView.as_view(), name='user-filter'),

    # Account
    path('accounts/deletion/request/', views.RequestDeletionView.as_view(), name='request-deletion'),

    # Preferences
    path('preferences/', views.UserPreferenceView.as_view(), name='user-preferences'),
    path('preferences/language/', views.LanguagePreferenceView.as_view(), name='language-preference'),
    path('preferences/timezone/', views.TimezonePreferenceView.as_view(), name='timezone-preference'),

    # Groups and Permissions
    # path('groups/', views.GroupListView.as_view(), name='group-list'),
    # path('groups/<int:group_id>/', views.GroupDetailView.as_view(), name='group-detail'),
    # path('groups/<int:group_id>/permissions/', views.GroupPermissionAPIView.as_view(), name='group-permission-list-add'),
    # path('groups/<int:group_id>/users/', views.GroupUserView.as_view(), name='group-users'),
    # path('permissions/', views.PermissionListView.as_view(), name='permission-list'),
    # path('permissions/<int:permission_id>/', views.PermissionDetailView.as_view(), name='permission-detail'),

    # MFA
    path('mfa/', views.SetupMFAView.as_view(), name='mfa-setup'),
    path('mfa/verify/', views.VerifyMFAView.as_view(), name='mfa-verify'),
    path('mfa/disable/', views.DisableMFAView.as_view(), name='mfa-disable'),

    # Social Authentication
    path('social/google/', views.GoogleAuthView.as_view(), name='social-google'),
    path('social/facebook/', views.FacebookAuthView.as_view(), name='social-facebook'),
    path('social/linkedin/', views.LinkedInAuthView.as_view(), name='social-linkedin'),

    path('logs/', views.UserActivityLogApiView.as_view(), name='activity-log'),
    path('avatar/', views.UserAvatarApiView.as_view(), name='user-avatar'),
    path('security-questions/', views.UserSecurityQuestionApiView.as_view(), name='security-questions'),
]
