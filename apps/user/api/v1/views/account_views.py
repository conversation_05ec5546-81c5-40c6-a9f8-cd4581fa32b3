import json
from django.http import HttpResponse
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.translation import gettext_lazy as _

from apps.identity.services import UserService
from apps.core.services import SystemSettingService
from apps.user.services import UserService as UserAccountService

logger = Logger(__name__)


class RequestDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.system_settings_service = SystemSettingService()

    def post(self, request):
        try:
            user_id = request.user.id
            user = self.user_service.get(id=user_id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            system_settings = self.system_settings_service.get(company=user.company)
            if not system_settings:
                result = {
                    "message": _("System settings not configured")
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            days_until_deletion = system_settings.days_until_account_deletion

            scheduled_time, scheduled = self.user_service.request_account_deletion(
                user=user,
                days_until_deletion=days_until_deletion
            ) 
            if not scheduled:
                result = {
                    "message": _("Failed to schedule account deletion")
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            result = {
                "message": f'Account will be deleted in {days_until_deletion} days. Log in to cancel.',
                "data": {
                    "scheduled_in_days": days_until_deletion,
                    "scheduled_at": scheduled_time
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "RequestDeletionView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e