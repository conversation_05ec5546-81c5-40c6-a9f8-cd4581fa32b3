from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.dj_rest_auth import SocialLoginView
from dxh_libraries.allauth import <PERSON><PERSON><PERSON>2<PERSON>dapter, <PERSON><PERSON><PERSON>2<PERSON>dapter, LinkedInOAuth2Adapter
from dxh_common.logger import Logger

from config.settings import GOOGLE_REDIRECT_URL, FACEBOOK_REDIRECT_URL, LINKEDIN_REDIRECT_URL
from apps.identity.services import UserDeviceService, UserSessionService
from apps.user.api.v1.serializers.user_serializers import UserDetailSerializer
from apps.user.utils.client import CustomOAuth2Client
from apps.user.services.user_preference_service import UserPreferenceService
from apps.identity.services import AuthService

logger = Logger(__name__)


class GoogleAuthView(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = GOOGLE_REDIRECT_URL
    client_class = CustomOAuth2Client

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            # user_device = self.user_device_service.create_user_device(user=user, user_agent=user_agent)
            # user_session = self.user_session_service.create_user_session(user, user_agent, user_device, ip_address, tokens)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error({"event": "GoogleAuthView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FacebookAuthView(SocialLoginView):
    adapter_class = FacebookOAuth2Adapter
    client_class = CustomOAuth2Client
    callback_url = FACEBOOK_REDIRECT_URL

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            # user_device = self.user_device_service.create_user_device(user=user, user_agent=user_agent)
            # user_session = self.user_session_service.create_user_session(user, user_agent, user_device, ip_address, tokens)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error({"event": "FacebookAuthView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e
        

class LinkedInAuthView(SocialLoginView):
    adapter_class = LinkedInOAuth2Adapter
    callback_url = LINKEDIN_REDIRECT_URL
    client_class = CustomOAuth2Client

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "LinkedInAuthView:get_response",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e