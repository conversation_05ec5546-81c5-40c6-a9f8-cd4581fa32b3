from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status, serializers
from dxh_libraries.dj_rest_auth import SocialLoginView
from dxh_libraries.allauth import Google<PERSON><PERSON>2<PERSON>dapter, FacebookOAuth2Adapter, LinkedInOAuth2Adapter
from dxh_common.logger import Logger

from django.http import HttpRequest, HttpResponseBadRequest
from django.urls import reverse, NoReverseMatch
from django.contrib.auth import get_user_model
from django.db import IntegrityError

from allauth.socialaccount.helpers import complete_social_login
from allauth.socialaccount import app_settings as allauth_account_settings
from allauth.socialaccount.providers.oauth2.client import OAuth2Error
from requests.exceptions import HTTPError

from config.settings import GOOGLE_REDIRECT_URL, FACEBOOK_REDIRECT_URL, LINKEDIN_REDIRECT_URL
from apps.identity.services import UserDeviceService, UserSessionService
from apps.user.api.v1.serializers.user_serializers import UserDetailSerializer
from apps.user.utils.client import CustomOA<PERSON>2Client
from apps.user.services.user_preference_service import UserPreferenceService
from apps.identity.services import AuthService

logger = Logger(__name__)


class LinkedInSocialLoginSerializer(serializers.Serializer):
    """
    Custom serializer for LinkedIn OAuth2 login to handle LinkedIn-specific token validation.
    """
    access_token = serializers.CharField(required=False, allow_blank=True)
    code = serializers.CharField(required=False, allow_blank=True)
    id_token = serializers.CharField(required=False, allow_blank=True)

    def _get_request(self):
        request = self.context.get('request')
        if not isinstance(request, HttpRequest):
            request = request._request
        return request

    def get_social_login(self, adapter, app, token, response):
        """
        Get social login with enhanced error handling for LinkedIn.
        """
        request = self._get_request()
        social_login = adapter.complete_login(request, app, token, response=response)
        social_login.token = token
        return social_login

    def set_callback_url(self, view, adapter_class):
        # first set url from view
        self.callback_url = getattr(view, 'callback_url', None)
        if not self.callback_url:
            # auto generate base on adapter and request
            try:
                self.callback_url = reverse(
                    viewname=adapter_class.provider_id + '_callback',
                    request=self._get_request(),
                )
            except NoReverseMatch:
                raise serializers.ValidationError(
                    _('Define callback_url in view'),
                )

    def validate(self, attrs):
        view = self.context.get('view')
        request = self._get_request()

        if not view:
            raise serializers.ValidationError(
                _('View is not defined, pass it as a context variable'),
            )

        adapter_class = getattr(view, 'adapter_class', None)
        if not adapter_class:
            raise serializers.ValidationError(_('Define adapter_class in view'))

        adapter = adapter_class(request)
        app = adapter.get_provider().app

        access_token = attrs.get('access_token')
        code = attrs.get('code')
        id_token = attrs.get('id_token')

        # Case 1: We received the access_token
        if access_token:
            tokens_to_parse = {'access_token': access_token}
            token = access_token
            # For LinkedIn with id_token (similar to Google's OpenID Connect)
            if id_token:
                tokens_to_parse['id_token'] = id_token

        # Case 2: We received the authorization code
        elif code:
            self.set_callback_url(view=view, adapter_class=adapter_class)
            self.client_class = getattr(view, 'client_class', None)

            if not self.client_class:
                raise serializers.ValidationError(
                    _('Define client_class in view'),
                )

            provider = adapter.get_provider()
            scope = provider.get_scope_from_request(request)
            client = self.client_class(
                request,
                app.client_id,
                app.secret,
                adapter.access_token_method,
                adapter.access_token_url,
                self.callback_url,
                scope,
                scope_delimiter=adapter.scope_delimiter,
                headers=adapter.headers,
                basic_auth=adapter.basic_auth,
            )
            try:
                token = client.get_access_token(code)
            except OAuth2Error as ex:
                logger.error(f"LinkedIn OAuth2Error: {str(ex)}")
                raise serializers.ValidationError(
                    _('Failed to exchange code for access token')
                ) from ex
            access_token = token['access_token']
            tokens_to_parse = {'access_token': access_token}

            # If available we add additional data to the dictionary
            for key in ['refresh_token', 'id_token', adapter.expires_in_key]:
                if key in token:
                    tokens_to_parse[key] = token[key]
        else:
            raise serializers.ValidationError(
                _('Incorrect input. access_token or code is required.'),
            )

        social_token = adapter.parse_token(tokens_to_parse)
        social_token.app = app

        try:
            # Enhanced handling for LinkedIn
            if adapter.provider_id == 'linkedin_oauth2':
                # For LinkedIn, pass the response data including id_token if available
                response_data = {'access_token': access_token}
                if id_token:
                    response_data['id_token'] = id_token
                login = self.get_social_login(adapter, app, social_token, response_data)
            else:
                login = self.get_social_login(adapter, app, social_token, token)

            ret = complete_social_login(request, login)
        except HTTPError as e:
            logger.error(f"LinkedIn HTTPError during social login: {str(e)}")
            # More specific error handling for LinkedIn
            raise serializers.ValidationError(_('LinkedIn authentication failed. Please try again.'))
        except Exception as e:
            logger.error(f"LinkedIn unexpected error during social login: {str(e)}")
            raise serializers.ValidationError(_('Authentication failed. Please try again.'))

        if isinstance(ret, HttpResponseBadRequest):
            raise serializers.ValidationError(ret.content)

        if not login.is_existing:
            # We have an account already signed up in a different flow
            # with the same email address: raise an exception.
            if allauth_account_settings.UNIQUE_EMAIL:
                # Do we have an account already with this email address?
                account_exists = get_user_model().objects.filter(
                    email=login.user.email,
                ).exists()
                if account_exists:
                    raise serializers.ValidationError(
                        _('User is already registered with this e-mail address.'),
                    )

            login.lookup()
            try:
                login.save(request, connect=True)
            except IntegrityError as ex:
                logger.error(f"LinkedIn IntegrityError during user save: {str(ex)}")
                raise serializers.ValidationError(
                    _('User is already registered with this e-mail address.'),
                ) from ex

        attrs['user'] = login.account.user
        return attrs


class GoogleAuthView(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = GOOGLE_REDIRECT_URL
    client_class = CustomOAuth2Client

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            # user_device = self.user_device_service.create_user_device(user=user, user_agent=user_agent)
            # user_session = self.user_session_service.create_user_session(user, user_agent, user_device, ip_address, tokens)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error({"event": "GoogleAuthView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FacebookAuthView(SocialLoginView):
    adapter_class = FacebookOAuth2Adapter
    client_class = CustomOAuth2Client
    callback_url = FACEBOOK_REDIRECT_URL

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            # user_device = self.user_device_service.create_user_device(user=user, user_agent=user_agent)
            # user_session = self.user_session_service.create_user_session(user, user_agent, user_device, ip_address, tokens)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)
        
        except Exception as e:
            logger.error({"event": "FacebookAuthView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e
        

class LinkedInAuthView(SocialLoginView):
    adapter_class = LinkedInOAuth2Adapter
    callback_url = LINKEDIN_REDIRECT_URL
    client_class = CustomOAuth2Client
    serializer_class = LinkedInSocialLoginSerializer

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.auth_service = AuthService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()

    def get_response(self):
        try:
            user = self.user
            tokens = self.auth_service.get_tokens(user)

            self.user_preference_service.get_or_create_user_preference(user)

            serializer = UserDetailSerializer(user)
            result = {
                "message": _("Login successful."),
                "data": {
                    "tokens": tokens,
                    "user": serializer.data,
                },
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "LinkedInAuthView:get_response",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e