from django.http import HttpResponse
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from apps.user.services import UserExportService

logger = Logger(__name__)


class UserExportView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.export_service = UserExportService()

    def get(self, request, *args, **kwargs):
        format_type = request.query_params.get('format_type', 'csv')
        user_ids_param = request.query_params.get('user_ids')
        if user_ids_param:
            user_ids = user_ids_param.split(',')
            user_ids = list(map(int, user_ids))
        else:
            user_ids = None
        
        if not request.user.is_superuser:
            result = {
                "message": _("You do not have permission to access this resource.")
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        if format_type not in ['csv', 'json', 'html', 'tsv', 'yaml']:
            result = {
                "message": _("Invalid format type. Supported formats are: csv, json, html, tsv, yaml.")
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        try:
            file_content = self.export_service.export_data(format_type, user_ids)

            content_type = {
                'csv': 'text/csv',
                'json': 'application/json',
                'html': 'text/html',
                'tsv': 'text/tab-separated-values',
                'yaml': 'application/x-yaml'
            }.get(format_type, 'application/octet-stream')

            response = HttpResponse(file_content, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="users.{format_type}"'

            return response
        
        except Exception as e:
            logger.error({"event": "UserExportView:get",
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
