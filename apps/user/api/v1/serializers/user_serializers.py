from django.contrib.auth import get_user_model
from django.conf import settings
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.easy_thumbnails import get_thumbnailer
from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer

from apps.user.api.v1.serializers.user_preference_serializers import UserPreferenceDetailsSerializer
from apps.user.api.v1.serializers.user_profile_serializers import UserProfileDetailsSerializer
from apps.identity.services.navigation_permission_service import NavigationPermissionService

User = get_user_model()


class UserSerializer(BaseModelSerializer):
    class Meta:
        model = User
        fields = '__all__'
        read_only_fields = (
            'id', 'username', 'company', 'email', 'is_email_verified', 'is_phone_verified',
            'last_login', 'date_joined', 'is_superuser', 'is_staff'
        )


class UserDetailSerializer(BaseModelSerializer):
    preferences = UserPreferenceDetailsSerializer(
        source='userpreference',
        read_only=True
    )
    profile = UserProfileDetailsSerializer(
        source='user_profiles',
        read_only=True
    )
    groups = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'email', 'username', 'first_name', 'last_name', 'phone', 'company',
            'is_active', 'status', 'is_email_verified', 'is_phone_verified', 'preferences',
            'profile', 'groups', 'permissions', 'last_login', 'date_joined'
        )
        read_only_fields = (
            'id', 'username', 'company', 'email', 'is_email_verified', 'is_phone_verified',
            'last_login', 'date_joined'
        )

    def get_groups(self, obj):
        return list(obj.groups.values("id", "name", "code"))

    def get_permissions(self, obj):
        user_permissions = obj.user_permissions.values_list("codename", flat=True)
        group_permissions = obj.groups.values_list("permissions__codename", flat=True)
        group_permissions = [perm for perm in group_permissions if perm is not None]

        navigation_permission_service = NavigationPermissionService()
        group = obj.groups.first()
        group_id = group.id if group else None
        navigation_permissions = navigation_permission_service.get_navigation_permission_by_group(group_id)


        return {
            "user_permissions": user_permissions,
            "group_permissions": group_permissions,
            "menu_permissions": navigation_permissions
        }


class UserListSerializer(BaseModelSerializer):
    groups = serializers.SerializerMethodField()
    profile_picture_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'email', 'username', 'first_name', 'last_name',
            'groups', 'profile_picture_url', 'is_active', 'is_deleted', 'status', 'created_at'
        )
        read_only_fields = ('id', 'created_at')

    def get_groups(self, obj):
        return list(obj.groups.values("id", "name", "code"))

    def get_profile_picture_url(self, obj):
        try:
            user_profile = getattr(obj, "user_profiles", None)
            if user_profile and user_profile.profile_picture:
                thumbnail = get_thumbnailer(user_profile.profile_picture)['profile']
                return settings.MEDIA_BASE_URL + thumbnail.url

            return None

        except Exception as e:
            return None


class UserCreateSerializer(BaseModelSerializer):
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={"input_type": "password"},
        error_messages={"required": _("Please confirm your password.")}
    )

    class Meta:
        model = User
        fields = [
            'email', 'username', 'password', 'confirm_password',
            'first_name', 'last_name', 'groups'
        ]
        read_only_fields = (
            'id', 'company', 'is_email_verified', 'is_phone_verified',
            'last_login', 'date_joined', 'is_superuser', 'is_staff'
        )

    def _validate_password(self, password, confirm_password):
        if password != confirm_password:
            raise serializers.ValidationError(
                {"password": _("Passwords do not match.")}
            )

        return password

    def validate(self, data):
        password = data.get('password')
        confirm_password = data.get('confirm_password')
        self._validate_password(password, confirm_password)

        return data