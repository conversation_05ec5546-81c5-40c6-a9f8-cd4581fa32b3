from django.test import TestCase
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

from apps.user.services.user_data_export_service import UserDataExportService
from apps.core.services import CompanyService

User = get_user_model()


class UserDataExportServiceTest(TestCase):
    def setUp(self):
        self.company_service = CompanyService()
        self.company = self.company_service.get_default_company()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            company=self.company
        )
        
        self.export_service = UserDataExportService()

    def test_export_user_data_basic(self):
        """Test basic user data export functionality"""
        result = self.export_service.export_user_data(self.user)
        
        # Check basic structure
        self.assertIn('message', result)
        self.assertIn('data', result)
        self.assertIn('exported_at', result)
        self.assertIn('user_id', result)
        self.assertIn('user_email', result)
        
        # Check data structure
        data = result['data']
        self.assertIn('profile', data)
        self.assertIn('activity_logs', data)
        self.assertIn('sessions', data)
        self.assertIn('notifications', data)
        self.assertIn('kanban_data', data)
        self.assertIn('preferences', data)
        self.assertIn('security_data', data)
        
        # Check user info
        self.assertEqual(result['user_id'], self.user.id)
        self.assertEqual(result['user_email'], self.user.email)

    def test_get_user_profile(self):
        """Test user profile data extraction"""
        profile_data = self.export_service._get_user_profile(self.user)
        
        self.assertIsInstance(profile_data, dict)
        # Profile should contain basic user info
        self.assertIn('id', profile_data)
        self.assertIn('email', profile_data)
        self.assertIn('username', profile_data)

    def test_get_activity_logs_empty(self):
        """Test activity logs when no logs exist"""
        activity_logs = self.export_service._get_activity_logs(self.user)
        
        self.assertIsInstance(activity_logs, list)
        self.assertEqual(len(activity_logs), 0)

    def test_get_user_sessions_empty(self):
        """Test user sessions when no sessions exist"""
        sessions = self.export_service._get_user_sessions(self.user)
        
        self.assertIsInstance(sessions, list)
        self.assertEqual(len(sessions), 0)

    def test_get_notifications_empty(self):
        """Test notifications when no notifications exist"""
        notifications = self.export_service._get_notifications(self.user)
        
        self.assertIsInstance(notifications, dict)
        self.assertIn('in_app_notifications', notifications)
        self.assertIn('email_notifications', notifications)
        self.assertEqual(len(notifications['in_app_notifications']), 0)
        self.assertEqual(len(notifications['email_notifications']), 0)

    def test_get_kanban_data_empty(self):
        """Test kanban data when no kanban data exists"""
        kanban_data = self.export_service._get_kanban_data(self.user)
        
        self.assertIsInstance(kanban_data, dict)
        self.assertIn('assigned_tasks', kanban_data)
        self.assertIn('created_tasks', kanban_data)
        self.assertIn('comments', kanban_data)
        self.assertIn('projects', kanban_data)

    def test_get_user_preferences_empty(self):
        """Test user preferences when no preferences exist"""
        preferences = self.export_service._get_user_preferences(self.user)
        
        self.assertIsInstance(preferences, dict)
        self.assertIn('user_preferences', preferences)
        self.assertIn('notification_preferences', preferences)

    def test_get_security_data_basic(self):
        """Test security data extraction"""
        security_data = self.export_service._get_security_data(self.user)
        
        self.assertIsInstance(security_data, dict)
        self.assertIn('mfa_setups', security_data)
        self.assertIn('security_questions', security_data)
        self.assertIn('account_status', security_data)
        
        # Check account status
        account_status = security_data['account_status']
        self.assertIn('is_active', account_status)
        self.assertIn('is_email_verified', account_status)
        self.assertIn('date_joined', account_status)

    @patch('apps.user.services.user_data_export_service.logger')
    def test_error_handling(self, mock_logger):
        """Test error handling in export service"""
        # Test with invalid user
        with patch.object(self.export_service, '_get_user_profile', side_effect=Exception("Test error")):
            result = self.export_service._get_user_profile(self.user)
            self.assertEqual(result, {})
            mock_logger.warning.assert_called()

    def test_service_error_on_failure(self):
        """Test that ServiceError is raised on major failures"""
        with patch.object(self.export_service, '_get_user_profile', side_effect=Exception("Critical error")):
            with self.assertRaises(Exception):
                self.export_service.export_user_data(self.user)
