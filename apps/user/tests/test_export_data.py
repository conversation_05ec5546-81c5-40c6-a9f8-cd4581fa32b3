import json
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from apps.core.models import Company, SystemSetting

User = get_user_model()


class UserDataExportAPITestCase(TestCase):
    """Test suite for User Data Export API endpoint"""

    def setUp(self):
        """Set up test data"""
        # Create a test company
        self.company = Company.objects.create(
            name='Test Company',
            email='<EMAIL>',
            url='https://testcompany.com',
            mobile_no='**********'
        )

        # Create system settings
        self.system_settings = SystemSetting.objects.create(
            company=self.company,
            days_until_account_deletion=30
        )

        # Create a test user
        self.user = User.objects.create(
            username='testuser',
            email='<EMAIL>',
            company=self.company,
            is_active=True,
            is_email_verified=True
        )
        self.user.set_password('testpassword123')
        self.user.save()

        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Define the API endpoint
        self.export_url = '/api/users/v1/accounts/export/'

    def test_export_api_endpoint_success(self):
        """Test the export API endpoint returns success"""
        response = self.client.get(self.export_url)

        # Should return a file download response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('attachment', response['Content-Disposition'])

        # Verify the response content is valid JSON
        try:
            # The response content should be valid JSON
            content = response.content.decode('utf-8')
            data = json.loads(content)

            # Check basic structure
            self.assertIn('message', data)
            self.assertIn('data', data)
            self.assertIn('exported_at', data)
            self.assertIn('user_id', data)
            self.assertIn('user_email', data)

        except json.JSONDecodeError:
            self.fail("Response content is not valid JSON")

    def test_export_api_endpoint_unauthenticated(self):
        """Test the export API endpoint requires authentication"""
        # Remove authentication
        self.client.force_authenticate(user=None)

        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
