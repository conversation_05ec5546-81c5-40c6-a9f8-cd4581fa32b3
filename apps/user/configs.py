from import_export import resources, fields
from django.contrib.auth import get_user_model
from dxh_common.logger import Logger

User = get_user_model()
logger = Logger(__name__)


class UserExportResource(resources.ModelResource):
    groups = fields.Field(attribute='groups', column_name='Groups')

    class Meta:
        model = User
        fields = (
            "id",
            "full_name",
            "email",
            "groups",
            "phone",
            "is_active",
        )
        export_order = fields
        import_id_fields = ('id',)
        skip_unchanged = True
        report_skipped = True

    def dehydrate_groups(self, user):
        try:
            groups = ", ".join([group.name for group in user.groups.all()])

            return groups
        
        except Exception as e:
            logger.error({"event": "UserExportResource:dehydrate_groups",
                          "message": "Error while dehydrating groups", "error": str(e)})
            return ""

    def export(self, user_ids=None, *args, **kwargs):
        try:
            if user_ids:
                queryset = self.get_queryset().filter(id__in=user_ids)
            else:
                queryset = self.get_queryset()

            dataset = super().export(queryset=queryset, *args, **kwargs)

            return dataset
        
        except Exception as e:
            logger.error({"event": "UserExportResource:export",
                          "message": "Error while exporting data", "error": str(e)})
            raise e

    def get_queryset(self):
        try:
            users = User.objects.prefetch_related('groups')

            return users
        
        except Exception as e:
            logger.error({"event": "UserExportResource:get_queryset",
                          "message": "Error while getting queryset", "error": str(e)})
            raise e