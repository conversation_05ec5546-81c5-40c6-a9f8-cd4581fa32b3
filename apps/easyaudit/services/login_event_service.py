from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError
from dxh_common.base.base_service import BaseService
from apps.easyaudit.repositories import LoginEventRepository
from apps.identity.utils import generate_device_id
from django.contrib.gis.geoip2 import GeoIP2

logger = Logger(__name__)


class LoginEventService(BaseService):
    def __init__(self):
        super().__init__(LoginEventRepository())
        self.geo_ip = GeoIP2()
    
    def get_os_distribution(self):
        try:
            os_counts = self.repository.get_os_count()

            return os_counts
        
        except Exception as e:
            logger.error(f"Error getting os count: {str(e)}")
            raise ServiceError(f"Error getting os count: {str(e)}")
        
    def create_login_event(self, login_event_payload):
        user = login_event_payload["user"]
        remote_ip = login_event_payload["remote_ip"]
        user_agent = login_event_payload["user_agent"]
        browser_user_agent = login_event_payload["browser_user_agent"]
        referer = login_event_payload["referer"]
        screen_resolution = login_event_payload["screen_resolution"]
        language = login_event_payload["language"]
        event_type = login_event_payload["event_type"]

        try:
            browser = user_agent.browser.family
            device=user_agent.device.family
            os_version = user_agent.os.version_string
            is_mobile = user_agent.is_mobile
            is_tablet = user_agent.is_tablet
            is_pc = user_agent.is_pc
            is_touch_capable = user_agent.is_touch_capable
            is_bot = user_agent.is_bot

            try:
                city_info = self.geo_ip.city(remote_ip)
                location = city_info['city'] if 'city' in city_info else ''

                login_event = self.repository.create(
                    company=user.company if user and user.company else None,
                    device=device,
                    user=user,
                    username=user.full_name if user and user.full_name else '',
                    login_type=event_type,
                    created_by=user,
                    updated_by=user,
                    remote_ip=remote_ip,
                    browser=browser,
                    user_agent=browser_user_agent,
                    location=location,
                    referer=referer,
                    screen_resolution=screen_resolution,
                    latitude=city_info['latitude'],
                    longitude=city_info['longitude'],
                    accuracy_radius=city_info['accuracy_radius'],
                    time_zone=city_info['time_zone'],
                    continent_name=city_info['continent_name'],
                    continent_code=city_info['continent_code'],
                    country=city_info['country_name'],
                    country_code=city_info['country_code'],
                    city=city_info['city'],
                    region=city_info['region_name'],
                    region_code=city_info['region_code'],
                    postal_code=city_info['postal_code'],
                    dma_code=city_info['dma_code'],
                    is_in_european_union=city_info['is_in_european_union'],
                    language=language,
                    device_type=user_agent.device.family,
                    os=user_agent.os.family,
                    os_version=os_version,
                    is_mobile=is_mobile,
                    is_tablet=is_tablet,
                    is_pc=is_pc,
                    is_touch_capable=is_touch_capable,
                    is_bot=is_bot
                )
                logger.info(f"Login event created: {login_event.id}")

                return
            
            except Exception as e:
                logger.error(f"GeoIP2 error: {e}")
                return
        
        except Exception as e:
            logger.error(f"Can't create Login event: {str(e)}")

            return
        