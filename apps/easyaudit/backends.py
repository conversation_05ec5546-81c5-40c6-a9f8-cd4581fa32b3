import logging

from apps.easyaudit.models import CR<PERSON>DEvent, LoginEvent, RequestEvent
from apps.easyaudit.services.login_event_service import LoginEventService

logger = logging.getLogger(__name__)


class ModelBackend:
    def __init__(self):
        self.login_event_service = LoginEventService()

    def request(self, request_info):
        return RequestEvent.objects.create(**request_info)

    def crud(self, crud_info):
        return CRUDEvent.objects.create(**crud_info)

    def login(self, login_info):
        return self.login_event_service.create_login_event(login_info)
