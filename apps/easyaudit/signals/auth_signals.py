from django.contrib.auth import get_user_model, signals
from django.db import transaction
from django.utils.module_loading import import_string

from apps.easyaudit.middleware.easyaudit import get_current_request
from apps.easyaudit.models import LoginEvent
from apps.easyaudit.settings import (
    DATABASE_ALIAS,
    LOGGING_BACKEND,
    REMOTE_ADDR_HEADER,
    WATCH_AUTH_EVENTS,
)
from apps.easyaudit.utils import should_propagate_exceptions

audit_logger = import_string(LOGGING_BACKEND)()


def user_logged_in(sender, request, user, **kwargs):
    try:
        with transaction.atomic(using=DATABASE_ALIAS):
            payload = {
                "user": user,
                "remote_ip": request.META.get(REMOTE_ADDR_HEADER, ""),
                "user_agent": request.user_agent,
                "browser_user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "referer": request.META.get("HTTP_REFERER", ""),
                "screen_resolution": request.META.get("HTTP_SCREEN_RESOLUTION", ""),
                "language": request.META.get('HTTP_ACCEPT_LANGUAGE', '').split(',')[0],
                "event_type": LoginEvent.LOGIN
            }
            audit_logger.login(payload)
            
    except Exception:
        if should_propagate_exceptions():
            raise


def user_logged_out(sender, request, user, **kwargs):
    try:
        with transaction.atomic(using=DATABASE_ALIAS):
            payload = {
                "user": user,
                "remote_ip": request.META.get(REMOTE_ADDR_HEADER, ""),
                "user_agent": request.user_agent,
                "browser_user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "referer": request.META.get("HTTP_REFERER", ""),
                "screen_resolution": request.META.get("HTTP_SCREEN_RESOLUTION", ""),
                "language": request.META.get('HTTP_ACCEPT_LANGUAGE', '').split(',')[0],
                "event_type": LoginEvent.LOGOUT
            }
            audit_logger.login(payload)

    except Exception:
        if should_propagate_exceptions():
            raise


def user_login_failed(sender, credentials, **kwargs):
    try:
        with transaction.atomic(using=DATABASE_ALIAS):
            request = get_current_request()
            payload = {
                "user": None,
                "remote_ip": request.META.get(REMOTE_ADDR_HEADER, ""),
                "user_agent": request.user_agent,
                "browser_user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "referer": request.META.get("HTTP_REFERER", ""),
                "screen_resolution": request.META.get("HTTP_SCREEN_RESOLUTION", ""),
                "language": request.META.get('HTTP_ACCEPT_LANGUAGE', '').split(',')[0],
                "event_type": LoginEvent.FAILED
            }
            audit_logger.login(payload)

    except Exception:
        if should_propagate_exceptions():
            raise


if WATCH_AUTH_EVENTS:
    signals.user_logged_in.connect(
        user_logged_in, dispatch_uid="easy_audit_signals_logged_in"
    )
    signals.user_logged_out.connect(
        user_logged_out, dispatch_uid="easy_audit_signals_logged_out"
    )
    signals.user_login_failed.connect(
        user_login_failed, dispatch_uid="easy_audit_signals_login_failed"
    )
