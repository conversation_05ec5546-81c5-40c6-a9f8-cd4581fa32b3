from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

class CRUDEvent(models.Model):
    CREATE = 1
    UPDATE = 2
    DELETE = 3
    M2M_CHANGE = 4
    M2M_CHANGE_REV = 5
    M2M_ADD = 6
    M2M_ADD_REV = 7
    M2M_REMOVE = 8
    M2M_REMOVE_REV = 9
    M2M_CLEAR = 10
    M2M_CLEAR_REV = 11

    TYPES = (
        (CREATE, _("Create")),
        (UPDATE, _("Update")),
        (DELETE, _("Delete")),
        (M2M_CHANGE, _("Many-to-Many Change")),
        (M2M_CHANGE_REV, _("Reverse Many-to-Many Change")),
        (M2M_ADD, _("Many-to-Many Add")),
        (M2M_ADD_REV, _("Reverse Many-to-Many Add")),
        (M2M_REMOVE, _("Many-to-Many Remove")),
        (M2M_REMOVE_REV, _("Reverse Many-to-Many Remove")),
        (M2M_CLEAR, _("Many-to-Many Clear")),
        (M2M_CLEAR_REV, _("Reverse Many-to-Many Clear")),
    )

    event_type = models.SmallIntegerField(choices=TYPES, verbose_name=_("Event type"))
    object_id = models.CharField(max_length=255, verbose_name=_("Object ID"))
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        db_constraint=False,
        verbose_name=_("Content type"),
    )
    object_repr = models.TextField(
        default="", blank=True, verbose_name=_("Object representation")
    )
    object_json_repr = models.TextField(
        default="", blank=True, verbose_name=_("Object JSON representation")
    )
    changed_fields = models.TextField(
        default="", blank=True, verbose_name=_("Changed fields")
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        db_constraint=False,
        verbose_name=_("User"),
    )
    user_pk_as_string = models.CharField(
        max_length=255,
        default="",
        blank=True,
        help_text=_("String version of the user pk"),
        verbose_name=_("User PK as string"),
    )
    datetime = models.DateTimeField(auto_now_add=True, verbose_name=_("Date time"))

    class Meta:
        verbose_name = _("CRUD event")
        verbose_name_plural = _("CRUD events")
        ordering = ["-datetime"]
        indexes = [models.Index(fields=["object_id", "content_type"])]

    def is_create(self):
        return self.event_type == self.CREATE

    def is_update(self):
        return self.event_type == self.UPDATE

    def is_delete(self):
        return self.event_type == self.DELETE


class LoginEvent(BaseModel):
    LOGIN = 0
    LOGOUT = 1
    FAILED = 2
    TYPES = (
        (LOGIN, _("Login")),
        (LOGOUT, _("Logout")),
        (FAILED, _("Failed login")),
    )
    login_type = models.SmallIntegerField(choices=TYPES, verbose_name=_("Event type"))
    username = models.CharField(
        max_length=255, default="", blank=True, verbose_name=_("Username")
    )
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_login_events",
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        db_constraint=False,
        verbose_name=_("User"),
    )
    remote_ip = models.CharField(
        max_length=50, default="", db_index=True, verbose_name=_("Remote IP")
    )
    datetime = models.DateTimeField(auto_now_add=True, verbose_name=_("Date time"))
    device = models.CharField(
        max_length=255, 
        null=True, 
        verbose_name=_("Device")
    )
    os = models.CharField(
        max_length=255, 
        blank=True, 
        null=True, 
        verbose_name=_("OS")
    )
    os_version = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        verbose_name=_("OS Version")
    )
    browser = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Browser"),
    )
    device_type = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Device Type"),
    )
    location = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Location"),
    )
    accuracy_radius = models.IntegerField(
        null=True,
        blank=True,
        verbose_name=_("Accuracy Radius"),
        help_text=_("Radius in kilometers around the latitude and longitude where the device is likely located."),
    )
    city = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("City"),
    )
    continent_code = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Continent Code"),
    )
    continent_name = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Continent Name"),
    )
    country_code = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Country Code"),
    )
    country = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Country Name"),
    )
    is_in_european_union = models.BooleanField(
        default=False,
        verbose_name=_("Is in European Union"),
    )
    metro_code = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Metro Code"),
    )
    postal_code = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Postal Code"),
    )
    region = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Region Name"),
    )
    region_code = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Region Code"),
    )
    time_zone = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Time Zone"),
    )
    dma_code = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("DMA Code"),
        help_text=_("Designated Market Area code, used in the United States to identify television markets."),
    )
    
    latitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True,
        verbose_name=_("Latitude"),
    )
    longitude = models.DecimalField(
        max_digits=9,
        decimal_places=6,
        null=True,
        blank=True,
        verbose_name=_("Longitude"),
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("User Agent"),
    )
    screen_resolution = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Screen Resolution"),
    )
    referer = models.URLField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name=_("Referer"),
    )
    language = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Language"),
    )
    is_mobile = models.BooleanField(
        default=False, 
        verbose_name=_("Is Mobile")
    )
    is_tablet = models.BooleanField(
        default=False, 
        verbose_name=_("Is Tablet")
    )
    is_pc = models.BooleanField(
        default=False, 
        verbose_name=_("Is Desktop")
    )
    is_touch_capable = models.BooleanField(
        default=False, 
        verbose_name=_("Is Touch Capable")
    )
    is_bot = models.BooleanField(
        default=False, 
        verbose_name=_("Is Bot")
    )

    class Meta:
        verbose_name = _("login event")
        verbose_name_plural = _("login events")
        ordering = ["-datetime"]


class RequestEvent(models.Model):
    url = models.CharField(null=False, db_index=True, max_length=254, verbose_name=_("URL"))
    method = models.CharField(
        max_length=20, null=False, db_index=True, verbose_name=_("Method")
    )
    query_string = models.TextField(default="", verbose_name=_("Query string"))
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        db_constraint=False,
        verbose_name=_("User"),
    )
    remote_ip = models.CharField(
        max_length=50, default="", db_index=True, verbose_name=_("Remote IP")
    )
    datetime = models.DateTimeField(
        auto_now_add=True, db_index=True, verbose_name=_("Date time")
    )

    class Meta:
        verbose_name = _("request event")
        verbose_name_plural = _("request events")
        ordering = ["-datetime"]
