from django.db import DatabaseError
from django.db.models import Count
from dxh_common.base.base_repository import BaseRepository, RepositoryError
from apps.easyaudit.models import LoginEvent


class LoginEventRepository(BaseRepository):
    def __init__(self):
        super().__init__(LoginEvent)
        
    def get_os_count(self):
        try:
            os_count = (
                self.model.objects.values("os")
                .annotate(count=Count("os"))
                .order_by("-count")
            )
            
            return os_count
        
        except DatabaseError as e:
            raise RepositoryError(f"Error retrieving data: {e}")