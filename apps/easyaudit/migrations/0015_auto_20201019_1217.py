# Generated by Django 3.1.1 on 2020-10-19 12:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('easyaudit', '0014_auto_20200513_0008'),
    ]

    operations = [
        migrations.AlterField(
            model_name='crudevent',
            name='changed_fields',
            field=models.TextField(blank=True, null=True, verbose_name='Changed fields'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='content_type',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='Content type'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, 'Create'), (2, 'Update'), (3, 'Delete'), (4, 'Many-to-Many Change'), (5, 'Reverse Many-to-Many Change'), (6, 'Many-to-Many Add'), (7, 'Reverse Many-to-Many Add'), (8, 'Many-to-Many Remove'), (9, 'Reverse Many-to-Many Remove')], verbose_name='Event type'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_id',
            field=models.CharField(max_length=255, verbose_name='Object ID'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_json_repr',
            field=models.TextField(blank=True, null=True, verbose_name='Object JSON representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_repr',
            field=models.TextField(blank=True, null=True, verbose_name='Object representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user_pk_as_string',
            field=models.CharField(blank=True, help_text='String version of the user pk', max_length=255, null=True, verbose_name='User PK as string'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='login_type',
            field=models.SmallIntegerField(choices=[(0, 'Login'), (1, 'Logout'), (2, 'Failed login')], verbose_name='Event type'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True, verbose_name='Remote IP'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='username',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Username'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='method',
            field=models.CharField(db_index=True, max_length=20, verbose_name='Method'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='query_string',
            field=models.TextField(null=True, verbose_name='Query string'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True, verbose_name='Remote IP'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='url',
            field=models.CharField(db_index=True, max_length=254, verbose_name='URL'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
    ]
