# Generated by Django 1.11.6 on 2017-10-18 12:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('easyaudit', '0005_auto_20170713_1155'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.CharField(db_index=True, max_length=255)),
                ('method', models.CharField(db_index=True, max_length=20)),
                ('query_string', models.CharField(max_length=255, null=True)),
                ('remote_ip', models.Char<PERSON>ield(db_index=True, max_length=50, null=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-datetime'],
                'verbose_name': 'request event',
                'verbose_name_plural': 'request events',
            },
        ),
        migrations.AddField(
            model_name='loginevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True),
        ),
    ]
