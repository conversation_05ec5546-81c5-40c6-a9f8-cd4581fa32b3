# Generated by Django 5.1.5 on 2025-05-27 10:31

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_filetype_company_language_company'),
        ('easyaudit', '0004_auto_20170620_1354_squashed_0019_alter_crudevent_changed_fields_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='loginevent',
            name='accuracy_radius',
            field=models.IntegerField(blank=True, help_text='Radius in kilometers around the latitude and longitude where the device is likely located.', null=True, verbose_name='Accuracy Radius'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='browser',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Browser'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='City'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_login_events', to='core.company', verbose_name='Company'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='continent_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Continent Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='continent_name',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Continent Name'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Country Name'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='country_code',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Country Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True, default=datetime.datetime(2025, 5, 27, 10, 31, 34, 944646, tzinfo=datetime.timezone.utc), verbose_name='Created at'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='loginevent',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='Created by'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='deleted_at',
            field=models.DateTimeField(blank=True, default=None, null=True, verbose_name='Deleted at'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='deleted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL, verbose_name='Deleted by'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='device',
            field=models.CharField(max_length=255, null=True, verbose_name='Device'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='device_type',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Device Type'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='dma_code',
            field=models.CharField(blank=True, help_text='Designated Market Area code, used in the United States to identify television markets.', max_length=10, null=True, verbose_name='DMA Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is active'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_authenticated',
            field=models.BooleanField(default=False, verbose_name='Is Authenticated'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_bot',
            field=models.BooleanField(default=False, verbose_name='Is Bot'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_deleted',
            field=models.BooleanField(default=False, verbose_name='Is deleted'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_in_european_union',
            field=models.BooleanField(default=False, verbose_name='Is in European Union'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_mobile',
            field=models.BooleanField(default=False, verbose_name='Is Mobile'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_pc',
            field=models.BooleanField(default=False, verbose_name='Is Desktop'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_tablet',
            field=models.BooleanField(default=False, verbose_name='Is Tablet'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='is_touch_capable',
            field=models.BooleanField(default=False, verbose_name='Is Touch Capable'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='language',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Language'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='Latitude'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='location',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Location'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True, verbose_name='Longitude'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='metro_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Metro Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='os',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='OS'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='os_version',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='OS Version'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Postal Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='referer',
            field=models.URLField(blank=True, null=True, verbose_name='Referer'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='region',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Region Name'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='region_code',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Region Code'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='screen_resolution',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Screen Resolution'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='time_zone',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Time Zone'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True, verbose_name='Updated at'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='Updated by'),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='user_agent',
            field=models.TextField(blank=True, null=True, verbose_name='User Agent'),
        ),
    ]
