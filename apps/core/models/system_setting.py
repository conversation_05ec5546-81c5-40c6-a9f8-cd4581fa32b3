from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.utils.constants import DATE_FORMATS, DATE_TIME_FORMATS, TIME_FORMATS
from dxh_common.base.base_model import BaseModel


class SystemSetting(BaseModel):
    company = models.OneToOneField(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        related_name="company_system_settings",
        verbose_name=_("Company")
    )
    app_name = models.CharField(
        max_length=100,
        default="My Application",
        help_text=_("The name of the application."),
        verbose_name=_("Application Name")
    )
    app_version = models.CharField(
        max_length=20,
        default="1.0.0",
        help_text=_("Version of the application."),
        verbose_name=_("Application Version")
    )
    default_date_format = models.Char<PERSON>ield(
        max_length=100,
        choices=DATE_FORMATS,
        verbose_name=_("Default Date Format")
    )
    default_datetime_formate = models.Char<PERSON><PERSON>(
        max_length=100,
        choices=DATE_TIME_FORMATS,
        verbose_name=_("Default DateTime Format")
    )
    time_format = models.Char<PERSON>ield(
        max_length=20,
        choices=TIME_FORMATS,
        verbose_name=_("Time Format")
    )
    default_currency = models.CharField(
        max_length=10,
        default="USD",
        help_text=_("Default currency for transactions."),
        verbose_name=_("Default Currency")
    )
    default_language = models.CharField(
        max_length=10,
        default="en",
        help_text=_("Default language for the system."),
        verbose_name=_("Default Language")
    )
    decimal_precision = models.PositiveIntegerField(
        default=2,
        help_text=_("Number of decimal places for numeric values."),
        verbose_name=_("Decimal Precision")
    )
    default_ordering = models.CharField(
        max_length=50,
        default="asc",
        choices=[("asc", _("Ascending")), ("desc", _("Descending"))],
        verbose_name=_("Default Ordering")
    )
    default_timezone = models.CharField(
        max_length=50,
        default="UTC",
        help_text=_("Default timezone for the application."),
        verbose_name=_("Default Timezone")
    )
    maintenance_mode_enabled = models.BooleanField(
        default=False,
        help_text=_("Enable or disable maintenance mode."),
        verbose_name=_("Maintenance Mode Enabled")
    )
    maintenance_message = models.TextField(
        default="The system is currently under maintenance.",
        help_text=_("Message displayed during maintenance."),
        verbose_name=_("Maintenance Message")
    )
    session_timeout = models.PositiveIntegerField(
        default=30,
        help_text=_("Session timeout duration in minutes."),
        verbose_name=_("Session Timeout (Minutes)")
    )
    pagination_size = models.PositiveIntegerField(
        default=10,
        help_text=_("Default number of items per page."),
        verbose_name=_("Pagination Size")
    )
    api_rate_limit = models.PositiveIntegerField(
        default=1000,
        help_text=_("API rate limit (requests per hour)."),
        verbose_name=_("API Rate Limit")
    )
    max_upload_size = models.PositiveIntegerField(
        default=5242880,
        help_text=_("Maximum upload size in bytes."),
        verbose_name=_("Max Upload Size (Bytes)")
    )
    caching_enabled = models.BooleanField(
        default=True,
        help_text=_("Enable or disable caching."),
        verbose_name=_("Caching Enabled")
    )
    cache_expiry_time = models.PositiveIntegerField(
        default=300,
        help_text=_("Default cache expiry time in seconds."),
        verbose_name=_("Cache Expiry Time (Seconds)")
    )
    otp_expire_time = models.PositiveIntegerField(
        default=10,
        help_text=_("Verification code expiry time in minutes."),
        verbose_name=_("Verification Code Expire Time (Minutes)")
    )
    otp_length = models.PositiveIntegerField(
        default=6,
        help_text=_("Verification code length."),
        verbose_name=_("Verification Code Length")
    )
    logging_enabled = models.BooleanField(
        default=True,
        help_text=_("Enable or disable logging."),
        verbose_name=_("Logging Enabled")
    )
    log_level = models.CharField(
        max_length=20,
        default="INFO",
        choices=[
            ("DEBUG", _("Debug")),
            ("INFO", _("Info")),
            ("WARNING", _("Warning")),
            ("ERROR", _("Error")),
        ],
        verbose_name=_("Log Level")
    )
    terms_of_service_url = models.URLField(
        default="",
        blank=True,
        help_text=_("URL for the terms of service."),
        verbose_name=_("Terms of Service URL")
    )
    privacy_policy_url = models.URLField(
        default="",
        blank=True,
        help_text=_("URL for the privacy policy."),
        verbose_name=_("Privacy Policy URL")
    )
    password_format = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Password Format")
    )
    login_attempts = models.IntegerField(
        default=3,
        verbose_name=_("Allowed Login Attempts")
    )
    days_until_account_deletion = models.IntegerField(
        default=30,
        verbose_name=_("Days Until Account Deletion"),
        help_text=_("Number of days after requesting account deletion until the account is actually deleted.")
    )

    class Meta:
        db_table = "core_system_settings"
        verbose_name = _("System settings")
        verbose_name_plural = _("System settings")

    def __str__(self):
        return self.app_name
