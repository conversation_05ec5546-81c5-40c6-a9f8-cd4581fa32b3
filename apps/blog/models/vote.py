from django.db import models
from django.contrib.auth import get_user_model

from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.blog.constants import VOTE_CHOICES
from apps.blog.models.article import Article

User = get_user_model()


class Vote(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_votes",
        verbose_name=_("Company"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="votes",
        verbose_name=_("User")
    )
    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="votes",
        verbose_name=_("Article")
    )
    value = models.IntegerField(
        choices=VOTE_CHOICES,
        verbose_name=_("Vote Value")
    )

    class Meta:
        db_table = "blog_votes"
        verbose_name = _("Vote")
        verbose_name_plural = _("Votes")

    def __str__(self):
        return f"{self.get_value_display()} by {self.user} on {self.article}"