from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

User = get_user_model()


class ReadingList(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reading_list',
        verbose_name=_("User")
    )
    article = models.ForeignKey(
        "blog.Article",
        on_delete=models.CASCADE,
        related_name='in_reading_lists',
        verbose_name=_("Article")
    )

    class Meta:
        db_table = "blog_reading_list"
        verbose_name = _("Reading List")
        verbose_name_plural = _("Reading Lists")
        unique_together = ('user', 'article')

    def __str__(self):
        return f"{self.user} added {self.article} to reading list"