from django.db import models

from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.blog.models.article import Article


class SEOMeta(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_seo_meta",
        verbose_name=_("Company"),
    )
    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="seo_meta",
        verbose_name=_("Article")
    )  
    meta_title = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name=_("Meta Title")
    )
    meta_description = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Meta Description")
    )
    keywords = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Keywords (comma-separated)")
    )

    class Meta:
        db_table = "blog_seo_meta"
        verbose_name = _("SEO Meta")
        verbose_name_plural = _("SEO Meta")

    def __str__(self):
        return f"SEO Meta for {self.article}"
