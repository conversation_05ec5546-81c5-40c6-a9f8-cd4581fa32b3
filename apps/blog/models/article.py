from django.db import models
from django.utils.text import slugify
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.blog.models.category import BlogCategory
from apps.blog.models.tag import Tag

User = get_user_model()


class Article(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_articles",
        verbose_name=_("Company"),
    )
    title = models.CharField(
        max_length=200,
        verbose_name=_("Title")
    )
    slug = models.SlugField(
        max_length=200,
        db_index=True,
        unique=True,
        verbose_name=_("Slug")
    )
    content = models.TextField(
        verbose_name=_("Content")
    )
    author = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="articles",
        verbose_name=_("Author")
    )
    category = models.ForeignKey(
        BlogCategory,
        on_delete=models.SET_NULL,
        null=True,
        related_name="articles",
        verbose_name=_("Category")
    )
    tags = models.ManyToManyField(
        Tag,
        related_name="articles",
        verbose_name=_("Tags")
    )
    featured_image = models.ImageField(
        upload_to='blog/featured_images/%Y/%m/%d/',
        null=True,
        blank=True,
        verbose_name=_("Featured Image")
    )
    is_published = models.BooleanField(
        default=False,
        verbose_name=_("Published")
    )
    
    class Meta:
        db_table = "blog_articles"
        verbose_name = _("Article")
        verbose_name_plural = _("Articles")

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.title)
            unique_slug = base_slug
            counter = 1
            while Article.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{counter}"
                counter += 1
                
            self.slug = unique_slug
        super().save(*args, **kwargs)