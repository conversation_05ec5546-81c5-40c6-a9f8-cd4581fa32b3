import os
import mimetypes
from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.blog.constants import MEDIA_TYPE_CHOICES
from apps.blog.models.article import Article

User = get_user_model()


class Media(BaseModel):
    def upload_to(instance, filename):
        return f"blog/media/{instance.article.id}/{filename}"
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_medias",
        verbose_name=_("Company"),
    )    
    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="media",
        verbose_name=_("Article")
    )
    file = models.FileField(
        upload_to=upload_to,
        verbose_name=_("File")
    )
    file_name = models.CharField(
        max_length=255,
        verbose_name=_("File Name"),
        blank=True,
        null=True
    )
    file_size = models.PositiveIntegerField(
        verbose_name=_("File Size (bytes)"),
        null=True,
        blank=True
    )
    mime_type = models.CharField(
        max_length=100,
        verbose_name=_("MIME Type"),
        blank=True,
        null=True
    )
    media_type = models.CharField(
        max_length=10,
        choices=MEDIA_TYPE_CHOICES,
        default='other',
        verbose_name=_("Media Type")
    )
    caption = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name=_("Caption")
    )

    class Meta:
        db_table = "blog_media"
        verbose_name = _("Media")
        verbose_name_plural = _("Media")

    def __str__(self):
        return f"{self.get_media_type_display()}: {self.file.name}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_name = os.path.basename(self.file.name)
            self.file_size = self.file.size

            mime_type, _ = mimetypes.guess_type(self.file.name)
            self.mime_type = mime_type if mime_type else 'application/octet-stream'

            if not self.media_type or self.media_type == 'other':
                if self.mime_type and self.mime_type.startswith('image'):
                    self.media_type = 'image'
                elif self.mime_type and self.mime_type.startswith('video'):
                    self.media_type = 'video'
                elif self.mime_type and self.mime_type == 'application/pdf':
                    self.media_type = 'pdf'
                else:
                    self.media_type = 'other'

        super().save(*args, **kwargs)