from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

from apps.blog.constants import FOLLOWABLE_TYPES

User = get_user_model()


class Follow(BaseModel):
    follower = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='follows',
        verbose_name=_("Follower")
    )
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="company_follows",
        verbose_name=_("Company"),
    )
    followable_type = models.CharField(
        max_length=20, 
        choices=FOLLOWABLE_TYPES, 
        verbose_name=_("Followable Type")
    )
    followable_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Followable ID")
    )


    class Meta:
        db_table = "blog_follows"
        verbose_name = _("Follow Up")
        verbose_name_plural = _("Follow Ups")
        unique_together = ('follower', 'followable_type', 'followable_id')

    def __str__(self):
        return f"{self.follower} follows {self.followable_type} with ID {self.followable_id}"