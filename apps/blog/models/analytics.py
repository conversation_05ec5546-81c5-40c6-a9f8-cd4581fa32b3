from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel


class ArticleAnalytics(BaseModel):
    article = models.ForeignKey(
        "blog.Article",
        on_delete=models.CASCADE,
        related_name='analytics',
        verbose_name=_("Article")
    )
    views = models.IntegerField(
        default=0,
        verbose_name=_("Views")
    )
    reads = models.IntegerField(
        default=0,
        verbose_name=_("Reads")
    )
    engagement = models.FloatField(
        default=0.0,
        verbose_name=_("Engagement")
    )

    class Meta:
        db_table = "blog_analytics"
        verbose_name = _("Article Analytics")
        verbose_name_plural = _("Article Analytics")

    def __str__(self):
        return f"Analytics for {self.article}"