from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_model import BaseModel

User = get_user_model()


class Bookmark(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='bookmarks',
        verbose_name=_("User")
    )
    article = models.ForeignKey(
        "blog.Article",
        on_delete=models.CASCADE,
        related_name='bookmarks',
        verbose_name=_("Article")
    )

    class Meta:
        db_table = "blog_bookmarks"
        verbose_name = _("Bookmark")
        verbose_name_plural = _("Bookmarks")
        unique_together = ('user', 'article')

    def __str__(self):
        return f"{self.user} bookmarked {self.article}"