from django.urls import path
from apps.blog.api.v1 import views


urlpatterns = [
    path('articles/', views.ArticleApiView.as_view(), name='article-list-create'),
    path('articles/<int:id>/', views.ArticleApiView.as_view(), name='article-detail-update-delete'),
    path('articles/<int:id>/analytics/', views.ArticleAnalyticsApiView.as_view(), name='article-analytics'),
    path('articles/<int:id>/read/', views.ArticleReadApiView.as_view(), name='article-read'),
    path('articles/yearly-count/', views.YearlyArticleCountApiView.as_view(), name='article-yearly-count'),

    path('bookmarks/', views.BookmarkApiView.as_view(), name='bookmark-list'),
    path('bookmarks/<int:article_id>/', views.BookmarkApiView.as_view(), name='bookmark-create-delete'),
    
    path('category/', views.BlogCategoryApiView.as_view(), name='blogcategory_list_create'),
    path('category/<int:id>/', views.BlogCategoryApiView.as_view(), name='blogcategory_detail_update_delete'),

    path('comments/', views.CommentApiView.as_view(), name='comment_list_create'),
    path('comments/counts/', views.CommentCountApiView.as_view(), name='comment-counts'),
    path('comments/<int:id>/', views.CommentApiView.as_view(), name='comment_detail_update_delete'),

    path('follow/', views.FollowApiView.as_view(), name='follow-list-create'),
    path('follow/<int:id>/', views.FollowApiView.as_view(), name='unfollow'),

    path('media/', views.MediaApiView.as_view(), name='media_list_create'),
    path('media/<int:id>/', views.MediaApiView.as_view(), name='media_detail_update_delete'),

    path('reading-list/', views.ReadingListApiView.as_view(), name='reading_list_create'),
    path('reading-list/<int:id>/', views.ReadingListApiView.as_view(), name='reading_instance_remove'),

    path('seo-meta/', views.SEOMetaApiView.as_view(), name='seometa_list_create'),
    path('seo-meta/<int:id>/', views.SEOMetaApiView.as_view(), name='seometa_detail_update_delete'),

    path('tags/', views.TagApiView.as_view(), name='tag_list_create'),
    path('tags/<int:id>/', views.TagApiView.as_view(), name='tag_detail_update_delete'),

    path('votes/', views.VoteApiView.as_view(), name='vote-list-create'),
    path('votes/counts/', views.VoteCountApiView.as_view(), name='vote-counts'),
    path('votes/<int:id>/', views.VoteApiView.as_view(), name='vote-detail-update-delete'),

]
