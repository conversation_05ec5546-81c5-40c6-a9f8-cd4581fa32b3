from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer

from apps.blog.models import Article


class ArticleDetailSerializer(BaseModelSerializer):
    class Meta:
        model = Article
        fields = '__all__'
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']


class ArticleSerializer(BaseModelSerializer):
    tags = serializers.StringRelatedField(many=True)
    category = serializers.StringRelatedField()
    author = serializers.StringRelatedField()
    truncated_content = serializers.SerializerMethodField()

    class Meta:
        model = Article
        fields = [
            "id", "title", "slug", "truncated_content", "featured_image", "tags", "category", "author"
        ]

    def get_truncated_content(self, obj):
        content = obj.content or ""
        max_length = 150
        if len(content) > max_length:
            return f"{content[:max_length]}..."
        return content