from dxh_common.base.base_serializer import BaseModelSerializer
from apps.blog.models import ReadingList


class ReadingListSerializer(BaseModelSerializer):
    class Meta:
        model = ReadingList
        fields = ['user', 'article']
        validators = []
        
    def validate(self, data):
        """
        Perform standard validation but skip uniqueness check.
        The uniqueness check will be handled in the view.
        """
        return data