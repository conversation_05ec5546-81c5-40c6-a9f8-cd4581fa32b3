from config import settings
from dxh_libraries.rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer
from apps.blog.models import Media


class MediaSerializer(BaseModelSerializer):
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = Media
        fields = [
            'id',
            'article',
            'file_name',
            "file_size",
            "mime_type",
            "media_type",
            'caption',
            'file',
            'file_url',
            'created_at',
            'updated_at',
        ]

    def get_file_url(self, obj):
        if obj.file and obj.file.url:
            return f"{settings.MEDIA_BASE_URL}{obj.file.url}"
        return None
