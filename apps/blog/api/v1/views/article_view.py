from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import ArticleDetailSerializer, ArticleSerializer
from apps.blog.services import ArticleService, ArticleAnalyticsService, TagService

logger = Logger(__name__)


class ArticleApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.article_service = ArticleService()
        self.article_analytics_service = ArticleAnalyticsService()
        self.tag_service = TagService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                article = self.article_service.get(id=id)                
                if not article:
                    result = {
                        "message": _("Article not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)
                
                self.article_analytics_service.increment_views(article=article)

                serializer = ArticleDetailSerializer(article)
                result = {
                    "message": _("Article details retrieved"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            category_id = request.query_params.get("category_id", None)
            tag_ids = request.query_params.get("tag_ids", None)
            year = request.query_params.get("year", None)
            search_term = request.query_params.get("search", None)

            articles = self.article_service.get_all()
            articles = self.article_service.filter_by_category(articles, category_id)
            articles = self.article_service.filter_by_tags(articles, tag_ids)
            articles = self.article_service.filter_by_year(articles, year)
            articles = self.article_service.search(articles, search_term)

            paginated_data = self.paginator.paginate_queryset(articles, request)
            serializer = ArticleSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Articles retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ArticleApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            payload = request.data
            payload['user']=request.user.id
            serializer = ArticleDetailSerializer(data=payload)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            article_data = serializer.validated_data
            article = self.article_service.create_article(article_data)
            
            serializer = ArticleDetailSerializer(article)
            result = {
                "message": _("Article created successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ArticleApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            payload = request.data
            article = self.article_service.get(id=id)

            if not article:
                result = {
                    "message": _("Article not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = ArticleDetailSerializer(article, data=payload, partial=True)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            article_data = serializer.validated_data
            article = self.article_service.update_article(article, article_data)

            serializer = ArticleDetailSerializer(article)
            result = {
                "message": _("Article updated successfully"),
                "data": serializer.data,
            }
            
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ArticleApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            article = self.article_service.get(id=id)

            if not article:
                result = {
                    "message": _("Article not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            article= self.article_service.update(
                article, 
                is_deleted=True, 
                is_active=False, 
                deleted_by=request.user
            )

            serializer = ArticleDetailSerializer(article)
            result = {
                "message": _("Article deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "ArticleApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e


class YearlyArticleCountApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.article_service = ArticleService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            yearly_count = self.article_service.get_yearly_article_count()
            result = {
                "message": _("Yearly article counts retrieved successfully"),
                "data": list(yearly_count),
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "YearlyArticleCountApiView:get", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
           