from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.paginations import CustomPagination

from apps.blog.api.v1.serializers import VoteSerializer
from apps.blog.services.article_service import ArticleService
from apps.blog.services import VoteService

logger = Logger(__name__)


class VoteApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vote_service = VoteService()
        self.article_service = ArticleService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                instance = self.vote_service.get(id=id)
                if not instance:
                    result = {
                        "message": _("Vote not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = VoteSerializer(instance)
                result = {
                    "message": _("Vote retrieved successfully"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            article_id = request.query_params.get("article_id")
            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            votes = self.vote_service.list(article=article_id)
            paginated_data = self.paginator.paginate_queryset(votes, request)
            serializer = VoteSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Vote list retrieved successfully"),
                "data": records,
                "pagination": pagination
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "VoteApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            data = request.data
            data["user"] = request.user.id
            serializer = VoteSerializer(data=data)

            if not serializer.is_valid():
                result = {
                    "message": _("Something went wrong"),
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            vote_data = serializer.validated_data
            article = vote_data["article"]
            vote = self.vote_service.get(user=request.user, article=article)
            if vote:
                vote.value = vote_data["value"]
                vote.save()
                message = _("Vote updated successfully")
            else:
                vote = self.vote_service.create(**vote_data)
                message = _("Vote created successfully")


            serializer = VoteSerializer(vote)
            result = {
                "message": message,
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "VoteApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request):
        try:
            article_id = request.query_params.get("article_id")

            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            vote = self.vote_service.get(user=request.user, article_id=article_id)
            if not vote:
                result = {
                    "message": _("Vote not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            vote = self.vote_service.update(
                vote,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            serializer = VoteSerializer(vote)
            result = {
                "message": _("Vote deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "VoteApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e

class VoteCountApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vote_service = VoteService()
        
    def get(self, request):
        try:
            article_id = request.query_params.get("article_id")

            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            vote_counts = self.vote_service.get_vote_counts(article_id)

            result = {
                "message": _("Vote counts retrieved successfully"),
                "data": vote_counts,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "VoteApiView:get_vote_counts",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e