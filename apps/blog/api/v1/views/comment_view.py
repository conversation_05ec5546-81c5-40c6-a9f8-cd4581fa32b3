from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import CommentSerializer
from apps.blog.services import CommentService

logger = Logger(__name__)


class CommentApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.comment_service = CommentService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                comment = self.comment_service.get(id=id)
                if not comment:
                    result = {
                        "message": _("Comment not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = CommentSerializer(comment)
                result = {
                    "message": _("Comment details retrieved"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            article_id = request.query_params.get("article_id")
            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            comments = self.comment_service.list(article=article_id)
            paginated_data = self.paginator.paginate_queryset(comments, request)
            serializer = CommentSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Comments retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            payload = request.data
            payload['user'] = request.user.id
            serializer = CommentSerializer(data=payload)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            comment_data = serializer.validated_data
            comment = self.comment_service.create(**comment_data)

            serializer = CommentSerializer(comment)
            result = {
                "message": _("Comment created successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "CommentApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            payload = request.data
            comment = self.comment_service.get(id=id)

            if not comment:
                result = {
                    "message": _("Comment not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = CommentSerializer(comment, data=payload, partial=True)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            comment_data = serializer.validated_data
            comment = self.comment_service.update(comment, **comment_data)

            serializer = CommentSerializer(comment)
            result = {
                "message": _("Comment updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            comment = self.comment_service.get(id=id)
            if not comment:
                result = {
                    "message": _("Comment not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            comment = self.comment_service.update(
                comment,
                is_deleted=True, 
                is_active=False, 
                deleted_by=request.user
            )

            serializer = CommentSerializer(comment)
            result = {
                "message": _("Comment deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "CommentApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e

          
class CommentCountApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.comment_service = CommentService()

    def get(self, request):
        try:
            article_id = request.query_params.get("article_id")
            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            comment_count = self.comment_service.get_comment_counts(article_id)

            result = {
                "message": _("Comment count retrieved successfully"),
                "data": {
                    "comment_count": comment_count
                },
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "CommentCountApiView:get","message": "Unexpected error occurred","error": str(e)})
            raise e