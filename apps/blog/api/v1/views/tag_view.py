from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import TagSerializer
from apps.blog.services import TagService

logger = Logger(__name__)


class TagApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.tag_service = TagService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                tag = self.tag_service.get(id=id)

                if not tag:
                    result = {
                        "message": _("Tag not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = TagSerializer(tag)
                result = {
                    "message": _("Tag details retrieved"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            tags = self.tag_service.get_all().annotate(
                article_count=models.Count('articles')
            )
            paginated_data = self.paginator.paginate_queryset(tags, request)
            serializer = TagSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Tags retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TagApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            serializer = TagSerializer(data=request.data)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            tag_data = serializer.validated_data
            tag = self.tag_service.create(**tag_data)

            serializer = TagSerializer(tag)
            result = {
                "message": _("Tag created successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "TagApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            tag = self.tag_service.get(id=id)

            if not tag:
                result = {
                    "message": _("Tag not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = TagSerializer(tag, data=request.data, partial=True)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            tag_data = serializer.validated_data
            tag = self.tag_service.update(tag, **tag_data)

            serializer = TagSerializer(tag)
            result = {
                "message": _("Tag updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "TagApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            tag = self.tag_service.get(id=id)

            if not tag:
                result = {
                    "message": _("Tag not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            tag = self.tag_service.update(
                tag,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            serializer = TagSerializer(tag)
            result = {
                "message": _("Tag deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "TagApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e
