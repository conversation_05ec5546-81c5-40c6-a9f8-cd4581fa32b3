from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import BookmarkSerializer
from apps.blog.services import BookmarkService, ArticleService

logger = Logger(__name__)

class BookmarkApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.article_service = ArticleService()
        self.bookmark_service = BookmarkService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            bookmarks = self.bookmark_service.list_prefetch_order_by(request.user, '-updated_at')
            
            paginated_data = self.paginator.paginate_queryset(bookmarks, request)
            serializer = BookmarkSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Bookmarks retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "BookmarkApiView:get", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
        
    def post(self, request):
        try:
            payload = request.data
            payload["user"] = request.user

            serializer = BookmarkSerializer(data=payload)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            bookmark_data = serializer.validated_data
            bookmark, _ = self.bookmark_service.get_or_create(bookmark_data)

            serializer = BookmarkSerializer(bookmark)
            result = {
                "message": _("Bookmarked successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "BookmarkApiView:post", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
        
    def delete(self, request, article_id):
        try:
            article = self.article_service.get(id=article_id)
            if not article:
                result = {
                    "message": _("Article not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            bookmark = self.bookmark_service.get(user=request.user, article=article)
            if not bookmark:
                result = {
                    "message": _("Bookmark not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            self.bookmark_service.update(
                bookmark,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            result = {
                "message": _("Removed bookmark successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "BookmarkApiView:delete", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e