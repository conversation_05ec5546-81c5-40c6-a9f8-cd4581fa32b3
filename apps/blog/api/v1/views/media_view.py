from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import (
    Response, status, MultiPartParser, FormParser
)
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import MediaSerializer
from apps.blog.services import MediaService

logger = Logger(__name__)


class MediaApiView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.media_service = MediaService()

    def get(self, request, id=None):
        try:
            if id:
                media = self.media_service.get(id=id)
                if not media:
                    result = {
                        "message": _("Media not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = MediaSerializer(media)
                result = {
                    "message": _("Media retrieved successfully"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            article_id = request.query_params.get("article_id")
            
            if not article_id:
                result = {
                    "message": _("Article ID needed"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            medias = self.media_service.list(article=article_id)
            serializer = MediaSerializer(medias, many=True)
            result = {
                "message": _("Media list retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "MediaApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            serializer = MediaSerializer(data=request.data)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            media_data = serializer.validated_data
            media = self.media_service.create(**media_data)

            serializer = MediaSerializer(media)
            result = {
                "message": _("Media created successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "MediaApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            media = self.media_service.get(id=id)

            if not media:
                result = {
                    "message": _("Media not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = MediaSerializer(media, data=request.data, partial=True)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            media_data = serializer.validated_data
            media = self.media_service.update(media, **media_data)

            serializer = MediaSerializer(media)
            result = {
                "message": _("Media updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "MediaApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            media = self.media_service.get(id=id)

            if not media:
                result = {
                    "message": _("Media not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            media = self.media_service.update(
                media,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            serializer = MediaSerializer(media)
            result = {
                "message": _("Media deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "MediaApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e
