from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import FollowSerializer
from apps.blog.services import FollowService

logger = Logger(__name__)


class FollowApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.follow_service = FollowService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            follows = self.follow_service.list(follower=request.user).order_by("-created_at")
            paginated_data = self.paginator.paginate_queryset(follows, request)
            serializer = FollowSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Following instance retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FollowApiView:get", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            payload = request.data
            payload['follower'] = request.user.id
            serializer = FollowSerializer(data=payload)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            follow_data = serializer.validated_data
            self.follow_service.create(**follow_data)
            
            result = {
                "message": _("Followed successfully")
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FollowApiView:post", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
    
    def delete(self, request, id):
        try:
            follow = self.follow_service.get(id=id)
            if not follow:
                result = {
                    "message": _("Follow ups not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            follow.delete()
            result = {
                "message": _("Unfollowed successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "FollowApiView:delete", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
