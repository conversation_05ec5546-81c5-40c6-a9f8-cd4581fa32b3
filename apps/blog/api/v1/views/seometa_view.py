from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import SEOMetaSerializer
from apps.blog.services import SEOMetaService

logger = Logger(__name__)


class SEOMetaApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.seometa_service = SEOMetaService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                instance = self.seometa_service.get(id=id)
                
                if not instance:
                    result = {
                        "message": _("SEOMeta not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = SEOMetaSerializer(instance)
                result = {
                    "message": _("SEOMeta retrieved successfully"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            queryset = self.seometa_service.get_all()
            paginated_data = self.paginator.paginate_queryset(queryset, request)
            serializer = SEOMetaSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("SEOMeta list retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SEOMetaApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            serializer = SEOMetaSerializer(data=request.data)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            seo_data = serializer.validated_data
            seo = self.seometa_service.create(**seo_data)

            serializer = SEOMetaSerializer(seo)
            result = {
                "message": _("SEOMeta created successfully"),
                "data": serializer.data,
            }
            
            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "SEOMetaApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            seo_meta = self.seometa_service.get(id=id)

            if not seo_meta:
                result = {
                    "message": _("SEOMeta not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = SEOMetaSerializer(seo_meta, data=request.data, partial=True)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            seo_data = serializer.validated_data
            seo = self.seometa_service.update(seo_meta, **seo_data)

            serializer = SEOMetaSerializer(seo)
            result = {
                "message": _("SEOMeta updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "SEOMetaApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            seo_meta = self.seometa_service.get(id=id)

            if not seo_meta:
                result = {
                    "message": _("SEOMeta not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            seo = self.seometa_service.update(
                seo_meta,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            serializer = SEOMetaSerializer(seo)
            result = {
                "message": _("SEOMeta deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "SEOMetaApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e
