from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import ReadingListSerializer
from apps.blog.services import ReadingListService
from apps.blog.models import Article

logger = Logger(__name__)


class ReadingListApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.reading_list_service = ReadingListService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            reading_list = self.reading_list_service.list(user=request.user)
            paginated_data = self.paginator.paginate_queryset(reading_list, request)
            serializer = ReadingListSerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Reading list retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ReadingListApiView:get", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            payload = request.data
            payload['user'] = request.user.id
            serializer = ReadingListSerializer(data=payload)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            reading_list_data = serializer.validated_data
            self.reading_list_service.get_or_create(reading_list_data)
            
            result = {
                "message": _("Article added in reading list successfully")
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ReadingListApiView:post", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
    
    def delete(self, request, id):
        try:
            reading_list = self.reading_list_service.get(id=id)

            if not reading_list:
                result = {
                    "message": _("Reading list not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            reading_list.delete()
            result = {
                "message": _("Removed from reading list successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "ReadingListApiView:delete", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e
