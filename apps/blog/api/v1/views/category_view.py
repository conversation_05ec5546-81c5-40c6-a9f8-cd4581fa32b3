from django.db import models
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.paginations import CustomPagination
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.api.v1.serializers import BlogCategorySerializer
from apps.blog.services import BlogCategoryService

logger = Logger(__name__)


class BlogCategoryApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.category_service = BlogCategoryService()
        self.paginator = CustomPagination()

    def get(self, request, id=None):
        try:
            if id:
                category = self.category_service.get(id=id)
                if not category:
                    result = {
                        "message": _("Category not found"),
                    }
                    return Response(result, status=status.HTTP_400_BAD_REQUEST)

                serializer = BlogCategorySerializer(category)
                result = {
                    "message": _("Category retrieved successfully"),
                    "data": serializer.data,
                }
                return Response(result, status=status.HTTP_200_OK)

            categories = self.category_service.get_all()
            categories = self.category_service.get_article_count(categories)
            
            paginated_data = self.paginator.paginate_queryset(categories, request)
            serializer = BlogCategorySerializer(paginated_data, many=True)
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Category list retrieved successfully"),
                "data": records,
                "pagination": pagination,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "BlogCategoryApiView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def post(self, request):
        try:
            payload = request.data
            serializer = BlogCategorySerializer(data=payload)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            category_data = serializer.validated_data
            category = self.category_service.create(**category_data)

            serializer = BlogCategorySerializer(category)
            result = {
                "message": _("BlogCategory created successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "BlogCategoryApiView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def put(self, request, id):
        try:
            payload = request.data
            category = self.category_service.get(id=id)

            if not category:
                result = {
                    "message": _("BlogCategory not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer = BlogCategorySerializer(category, data=payload, partial=True)

            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            category_data = serializer.validated_data
            category = self.category_service.update(instance=category, **category_data)

            serializer = BlogCategorySerializer(category)
            result = {
                "message": _("BlogCategory updated successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "BlogCategoryApiView:put", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, id):
        try:
            category = self.category_service.get(id=id)

            if not category:
                result = {
                    "message": _("BlogCategory not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            category = self.category_service.update(
                category,
                is_deleted=True,
                is_active=False,
                deleted_by=request.user
            )

            serializer = BlogCategorySerializer(category)
            result = {
                "message": _("BlogCategory deleted successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "BlogCategoryApiView:delete", "message": "Unexpected error occurred", "error": str(e)})
            raise e
