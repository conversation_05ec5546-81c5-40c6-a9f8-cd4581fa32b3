from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import Response, status
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.blog.services import ArticleAnalyticsService, ArticleService
from apps.blog.api.v1.serializers import ArticleAnalyticsSerializer

logger = Logger(__name__)


class ArticleAnalyticsApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.article_analytics_service = ArticleAnalyticsService()
        self.article_service = ArticleService()

    def get(self, request, id):
        try:
            article = self.article_service.get(id=id)
            if not article:
                result = {
                    "message": _("Article not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            analytics = self.article_analytics_service.get_or_create(article=article)

            serializer = ArticleAnalyticsSerializer(analytics)
            result = {
                "message": _("Article analytics retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ArticleAnalyticsApiView:get", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e


class ArticleReadApiView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.article_analytics_service = ArticleAnalyticsService()
        self.article_service = ArticleService()

    def post(self, request, id):
        try:
            article = self.article_service.get(id=id)
            if not article:
                result = {
                    "message": _("Article not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            self.article_analytics_service.increment_reads(article)

            result = {
                "message": _("Read recorded successfully"),
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ArticleReadApiView:post", 
                          "message": "Unexpected error occurred", "error": str(e)})
            raise e