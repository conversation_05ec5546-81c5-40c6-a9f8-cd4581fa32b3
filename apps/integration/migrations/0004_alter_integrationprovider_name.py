# Generated by Django 5.1.5 on 2025-05-16 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integration', '0003_alter_userintegration_access_token_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='integrationprovider',
            name='name',
            field=models.CharField(choices=[('google', 'Google'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('github', 'GitHub'), ('stripe', 'Stripe'), ('openai', 'OpenAI'), ('deepseek', 'DeepSeek')], max_length=100, unique=True, verbose_name='Provider Name'),
        ),
    ]
