# Generated by Django 5.1.5 on 2025-05-16 12:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integration', '0004_alter_integrationprovider_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='appintegration',
            name='client_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='Client ID'),
        ),
        migrations.AlterField(
            model_name='appintegration',
            name='client_secret',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Client Secret'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='integrationprovider',
            name='auth_url',
            field=models.URLField(blank=True, help_text='OAuth authorization endpoint', null=True),
        ),
        migrations.AlterField(
            model_name='integrationprovider',
            name='token_url',
            field=models.URLField(blank=True, null=True, verbose_name='Token URL'),
        ),
    ]
